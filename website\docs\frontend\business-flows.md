# 业务流程指南

> 🔄 **目标**: 理解核心业务流程，正确实现前端交互逻辑

## 🎯 核心业务流程概览

### 1. 用户认证流程
```mermaid
flowchart TD
    A[访问受保护页面] --> B{是否已登录?}
    B -->|否| C[跳转到登录页]
    B -->|是| D{Token是否有效?}
    D -->|否| C
    D -->|是| E[显示页面内容]
    
    C --> F[用户输入凭据]
    F --> G[调用登录API]
    G --> H{登录成功?}
    H -->|否| I[显示错误信息]
    H -->|是| J[保存Token]
    J --> K[跳转到目标页面]
    
    I --> F
```

### 2. 内容浏览流程
```mermaid
flowchart TD
    A[用户访问首页] --> B[加载事件列表]
    B --> C[显示事件卡片]
    C --> D[用户点击事件]
    D --> E[跳转到事件详情]
    E --> F[加载事件详细信息]
    F --> G[显示相关社团和作者]
    G --> H[用户可以收藏或分享]
```

## 🚀 具体实现示例

### 1. 用户注册和登录流程
```typescript
// src/flows/authentication.ts
import { authService } from '@/services/auth';
import { useAuthStore } from '@/stores/auth';
import { useRouter } from 'next/router';

export function useAuthenticationFlow() {
  const { login, register } = useAuthStore();
  const router = useRouter();
  
  // 注册流程
  const handleRegister = async (userData: {
    username: string;
    email: string;
    password: string;
  }) => {
    try {
      // 1. 调用注册API
      await register(userData);
      
      // 2. 注册成功，显示成功消息
      alert('注册成功！请登录您的账户。');
      
      // 3. 跳转到登录页
      router.push('/login');
      
    } catch (error: any) {
      // 错误处理已在store中完成
      console.error('Registration failed:', error);
    }
  };
  
  // 登录流程
  const handleLogin = async (credentials: {
    username: string;
    password: string;
  }) => {
    try {
      // 1. 调用登录API
      await login(credentials);
      
      // 2. 登录成功，获取重定向地址
      const redirectTo = router.query.redirect as string || '/';
      
      // 3. 跳转到目标页面
      router.push(redirectTo);
      
    } catch (error: any) {
      // 错误处理已在store中完成
      console.error('Login failed:', error);
    }
  };
  
  // 社交登录流程（预留）
  const handleSocialLogin = async (provider: 'google' | 'github') => {
    // 重定向到OAuth提供商
    window.location.href = `/auth/${provider}`;
  };
  
  return {
    handleRegister,
    handleLogin,
    handleSocialLogin,
  };
}
```

### 2. 事件浏览和收藏流程
```typescript
// src/flows/eventBrowsing.ts
import { useState, useCallback } from 'react';
import { api } from '@/lib/api';
import { useAuthStore } from '@/stores/auth';

export function useEventBrowsingFlow() {
  const [bookmarkedEvents, setBookmarkedEvents] = useState<Set<string>>(new Set());
  const { isAuthenticated } = useAuthStore();
  
  // 加载用户收藏的事件
  const loadBookmarkedEvents = useCallback(async () => {
    if (!isAuthenticated) return;
    
    try {
      const { data } = await api.GET('/bookmarks', {
        params: { query: { type: 'events' } }
      });
      
      const eventIds = new Set(data.items.map((item: any) => item.target_id));
      setBookmarkedEvents(eventIds);
    } catch (error) {
      console.error('Failed to load bookmarks:', error);
    }
  }, [isAuthenticated]);
  
  // 收藏/取消收藏事件
  const toggleBookmark = useCallback(async (eventId: string) => {
    if (!isAuthenticated) {
      alert('请先登录后再收藏');
      return;
    }
    
    const isBookmarked = bookmarkedEvents.has(eventId);
    
    try {
      if (isBookmarked) {
        // 取消收藏
        await api.DELETE('/bookmarks/{id}', {
          params: { path: { id: eventId } }
        });
        
        setBookmarkedEvents(prev => {
          const newSet = new Set(prev);
          newSet.delete(eventId);
          return newSet;
        });
      } else {
        // 添加收藏
        await api.POST('/bookmarks', {
          body: {
            target_type: 'event',
            target_id: eventId,
          }
        });
        
        setBookmarkedEvents(prev => new Set(prev).add(eventId));
      }
    } catch (error) {
      console.error('Failed to toggle bookmark:', error);
    }
  }, [isAuthenticated, bookmarkedEvents]);
  
  // 分享事件
  const shareEvent = useCallback(async (event: any) => {
    const shareData = {
      title: event.name,
      text: event.description,
      url: `${window.location.origin}/events/${event.id}`,
    };
    
    if (navigator.share) {
      // 使用原生分享API
      try {
        await navigator.share(shareData);
      } catch (error) {
        console.error('Share failed:', error);
      }
    } else {
      // 复制链接到剪贴板
      try {
        await navigator.clipboard.writeText(shareData.url);
        alert('链接已复制到剪贴板');
      } catch (error) {
        console.error('Copy failed:', error);
      }
    }
  }, []);
  
  return {
    bookmarkedEvents,
    loadBookmarkedEvents,
    toggleBookmark,
    shareEvent,
  };
}
```

### 3. 搜索和筛选流程
```typescript
// src/flows/searchAndFilter.ts
import { useState, useCallback, useEffect } from 'react';
import { useDebounce } from '@/hooks/useDebounce';
import { api } from '@/lib/api';

export interface SearchFilters {
  type: 'all' | 'events' | 'circles' | 'artists';
  dateRange?: {
    start: string;
    end: string;
  };
  location?: string;
  tags?: string[];
}

export function useSearchAndFilterFlow() {
  const [query, setQuery] = useState('');
  const [filters, setFilters] = useState<SearchFilters>({ type: 'all' });
  const [results, setResults] = useState([]);
  const [loading, setLoading] = useState(false);
  const [hasMore, setHasMore] = useState(false);
  const [page, setPage] = useState(1);
  
  const debouncedQuery = useDebounce(query, 300);
  
  // 执行搜索
  const performSearch = useCallback(async (
    searchQuery: string,
    searchFilters: SearchFilters,
    pageNum: number = 1,
    append: boolean = false
  ) => {
    if (!searchQuery.trim() && searchFilters.type === 'all') {
      setResults([]);
      return;
    }
    
    setLoading(true);
    
    try {
      const params: any = {
        q: searchQuery,
        type: searchFilters.type,
        page: pageNum.toString(),
        limit: '20',
      };
      
      // 添加日期筛选
      if (searchFilters.dateRange) {
        params.start_date = searchFilters.dateRange.start;
        params.end_date = searchFilters.dateRange.end;
      }
      
      // 添加位置筛选
      if (searchFilters.location) {
        params.location = searchFilters.location;
      }
      
      // 添加标签筛选
      if (searchFilters.tags?.length) {
        params.tags = searchFilters.tags.join(',');
      }
      
      const { data } = await api.GET('/search', {
        params: { query: params }
      });
      
      if (append) {
        setResults(prev => [...prev, ...data.items]);
      } else {
        setResults(data.items);
      }
      
      setHasMore(data.items.length === 20);
      
    } catch (error) {
      console.error('Search failed:', error);
      if (!append) {
        setResults([]);
      }
    } finally {
      setLoading(false);
    }
  }, []);
  
  // 加载更多结果
  const loadMore = useCallback(() => {
    if (!loading && hasMore) {
      const nextPage = page + 1;
      setPage(nextPage);
      performSearch(debouncedQuery, filters, nextPage, true);
    }
  }, [loading, hasMore, page, debouncedQuery, filters, performSearch]);
  
  // 重置搜索
  const resetSearch = useCallback(() => {
    setQuery('');
    setFilters({ type: 'all' });
    setResults([]);
    setPage(1);
    setHasMore(false);
  }, []);
  
  // 更新筛选条件
  const updateFilters = useCallback((newFilters: Partial<SearchFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
    setPage(1);
  }, []);
  
  // 监听搜索条件变化
  useEffect(() => {
    setPage(1);
    performSearch(debouncedQuery, filters, 1, false);
  }, [debouncedQuery, filters, performSearch]);
  
  return {
    query,
    setQuery,
    filters,
    updateFilters,
    results,
    loading,
    hasMore,
    loadMore,
    resetSearch,
  };
}
```

### 4. 内容管理流程（管理员）
```typescript
// src/flows/contentManagement.ts
import { useState, useCallback } from 'react';
import { api } from '@/lib/api';
import { usePermissions } from '@/hooks/usePermissions';

export function useContentManagementFlow() {
  const [selectedItems, setSelectedItems] = useState<Set<string>>(new Set());
  const permissions = usePermissions();
  
  // 批量操作
  const performBatchAction = useCallback(async (
    action: 'delete' | 'approve' | 'reject',
    itemType: 'events' | 'circles' | 'users'
  ) => {
    if (!permissions.canManageEvents && itemType === 'events') {
      alert('权限不足');
      return;
    }
    
    const items = Array.from(selectedItems);
    if (items.length === 0) {
      alert('请选择要操作的项目');
      return;
    }
    
    const confirmMessage = `确定要${action === 'delete' ? '删除' : action === 'approve' ? '批准' : '拒绝'}这${items.length}个项目吗？`;
    if (!confirm(confirmMessage)) {
      return;
    }
    
    try {
      // 批量操作API调用
      await api.POST(`/admin/${itemType}/batch`, {
        body: {
          action,
          ids: items,
        }
      });
      
      // 清空选择
      setSelectedItems(new Set());
      
      // 刷新列表
      window.location.reload();
      
    } catch (error) {
      console.error('Batch action failed:', error);
    }
  }, [selectedItems, permissions]);
  
  // 单项操作
  const performSingleAction = useCallback(async (
    itemId: string,
    action: 'delete' | 'approve' | 'reject' | 'edit',
    itemType: 'events' | 'circles' | 'users'
  ) => {
    try {
      switch (action) {
        case 'delete':
          if (confirm('确定要删除这个项目吗？')) {
            await api.DELETE(`/admin/${itemType}/{id}`, {
              params: { path: { id: itemId } }
            });
          }
          break;
          
        case 'approve':
          await api.PUT(`/admin/${itemType}/{id}/approve`, {
            params: { path: { id: itemId } }
          });
          break;
          
        case 'reject':
          await api.PUT(`/admin/${itemType}/{id}/reject`, {
            params: { path: { id: itemId } }
          });
          break;
          
        case 'edit':
          // 跳转到编辑页面
          window.location.href = `/admin/${itemType}/${itemId}/edit`;
          return;
      }
      
      // 刷新列表
      window.location.reload();
      
    } catch (error) {
      console.error('Action failed:', error);
    }
  }, []);
  
  // 切换选择状态
  const toggleSelection = useCallback((itemId: string) => {
    setSelectedItems(prev => {
      const newSet = new Set(prev);
      if (newSet.has(itemId)) {
        newSet.delete(itemId);
      } else {
        newSet.add(itemId);
      }
      return newSet;
    });
  }, []);
  
  // 全选/取消全选
  const toggleSelectAll = useCallback((allItemIds: string[]) => {
    setSelectedItems(prev => {
      if (prev.size === allItemIds.length) {
        return new Set();
      } else {
        return new Set(allItemIds);
      }
    });
  }, []);
  
  return {
    selectedItems,
    toggleSelection,
    toggleSelectAll,
    performBatchAction,
    performSingleAction,
  };
}
```

## 📱 移动端适配流程

### 响应式设计考虑
```typescript
// src/hooks/useResponsive.ts
import { useState, useEffect } from 'react';

export function useResponsive() {
  const [screenSize, setScreenSize] = useState({
    width: typeof window !== 'undefined' ? window.innerWidth : 0,
    height: typeof window !== 'undefined' ? window.innerHeight : 0,
  });
  
  useEffect(() => {
    const handleResize = () => {
      setScreenSize({
        width: window.innerWidth,
        height: window.innerHeight,
      });
    };
    
    window.addEventListener('resize', handleResize);
    return () => window.removeEventListener('resize', handleResize);
  }, []);
  
  return {
    ...screenSize,
    isMobile: screenSize.width < 768,
    isTablet: screenSize.width >= 768 && screenSize.width < 1024,
    isDesktop: screenSize.width >= 1024,
  };
}

// 移动端优化的搜索组件
export function MobileSearchFlow() {
  const { isMobile } = useResponsive();
  const [isSearchOpen, setIsSearchOpen] = useState(false);
  
  if (isMobile) {
    return (
      <>
        {/* 搜索按钮 */}
        <button
          onClick={() => setIsSearchOpen(true)}
          className="fixed bottom-4 right-4 bg-blue-500 text-white p-3 rounded-full shadow-lg"
        >
          🔍
        </button>
        
        {/* 全屏搜索界面 */}
        {isSearchOpen && (
          <div className="fixed inset-0 bg-white z-50">
            <div className="p-4">
              <div className="flex items-center mb-4">
                <button
                  onClick={() => setIsSearchOpen(false)}
                  className="mr-3"
                >
                  ←
                </button>
                <input
                  type="text"
                  placeholder="搜索..."
                  className="flex-1 px-3 py-2 border rounded"
                  autoFocus
                />
              </div>
              {/* 搜索结果 */}
            </div>
          </div>
        )}
      </>
    );
  }
  
  // 桌面端搜索界面
  return <DesktopSearchComponent />;
}
```

## 🔄 状态同步流程

### 实时数据更新
```typescript
// src/flows/realTimeUpdates.ts
import { useEffect, useCallback } from 'react';
import { useAuthStore } from '@/stores/auth';

export function useRealTimeUpdates() {
  const { isAuthenticated, user } = useAuthStore();
  
  // 轮询更新用户数据
  const pollUserData = useCallback(async () => {
    if (!isAuthenticated) return;
    
    try {
      const { data } = await api.GET('/auth/me');
      // 更新用户信息
      useAuthStore.getState().setUser(data.user);
    } catch (error) {
      console.error('Failed to poll user data:', error);
    }
  }, [isAuthenticated]);
  
  // 设置轮询
  useEffect(() => {
    if (!isAuthenticated) return;
    
    const interval = setInterval(pollUserData, 5 * 60 * 1000); // 5分钟
    return () => clearInterval(interval);
  }, [isAuthenticated, pollUserData]);
  
  // 页面可见性变化时刷新数据
  useEffect(() => {
    const handleVisibilityChange = () => {
      if (document.visibilityState === 'visible' && isAuthenticated) {
        pollUserData();
      }
    };
    
    document.addEventListener('visibilitychange', handleVisibilityChange);
    return () => document.removeEventListener('visibilitychange', handleVisibilityChange);
  }, [isAuthenticated, pollUserData]);
}
```

## 🎯 最佳实践总结

### 1. 错误处理
- 所有API调用都应该有错误处理
- 用户友好的错误提示
- 网络错误时提供重试选项

### 2. 加载状态
- 显示适当的加载指示器
- 防止重复提交
- 骨架屏提升用户体验

### 3. 数据缓存
- 合理使用本地缓存
- 实现数据预加载
- 离线功能支持

### 4. 用户体验
- 响应式设计
- 无障碍访问支持
- 性能优化

---

**恭喜！** 🎉 您已经完成了前端对接文档包的学习。现在您应该能够：
- 快速生成类型安全的API客户端
- 实现完整的用户认证流程
- 处理多语言和国际化
- 构建健壮的错误处理机制
- 理解核心业务流程

如有问题，请查看 [GitHub Issues](https://github.com/ayafeed/ayafeed-api/issues) 或参考 [完整API文档](../)。
