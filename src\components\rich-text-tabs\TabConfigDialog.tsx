'use client';

import { useState, useEffect } from 'react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Switch } from '@/components/ui/switch';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Badge } from '@/components/ui/badge';
import { AlertCircle, Loader2 } from 'lucide-react';
import { Alert, AlertDescription } from '@/components/ui/alert';
import type { TabConfig, EntityType, LanguageCode } from '@/hooks/useRichTextTabs';

interface TabConfigDialogProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  config?: TabConfig | null; // null 表示新建，有值表示编辑
  entityType: EntityType;
  languageCode: LanguageCode;
  onSave: (config: Omit<TabConfig, 'id' | 'key' | 'created_at' | 'updated_at'> | Partial<Pick<TabConfig, 'label' | 'placeholder' | 'icon' | 'sort_order' | 'is_active'>>) => Promise<void>;
  isLoading?: boolean;
}

export function TabConfigDialog({
  open,
  onOpenChange,
  config,
  entityType,
  languageCode,
  onSave,
  isLoading = false,
}: TabConfigDialogProps) {
  const [formData, setFormData] = useState({
    label: '',
    placeholder: '',
    icon: '',
    sort_order: 0,
    is_active: true,
    is_preset: false,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});

  const isEditing = !!config;
  const isPreset = config?.is_preset;

  // 初始化表单数据
  useEffect(() => {
    if (config) {
      setFormData({
        label: config.label,
        placeholder: config.placeholder || '',
        icon: config.icon || '',
        sort_order: config.sort_order,
        is_active: Boolean(config.is_active), // 确保转换为布尔值
        is_preset: Boolean(config.is_preset), // 确保转换为布尔值
      });
    } else {
      setFormData({
        label: '',
        placeholder: '',
        icon: '',
        sort_order: 0,
        is_active: true,
        is_preset: false,
      });
    }
    setErrors({});
  }, [config, open]);

  // 表单验证
  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.label.trim()) {
      newErrors.label = '标签名称不能为空';
    }

    if (formData.label.trim().length > 100) {
      newErrors.label = '标签名称不能超过 100 个字符';
    }

    if (formData.placeholder && formData.placeholder.length > 200) {
      newErrors.placeholder = '占位符文本不能超过 200 个字符';
    }

    if (formData.icon && formData.icon.length > 50) {
      newErrors.icon = '图标名称不能超过 50 个字符';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // 处理保存
  const handleSave = async () => {
    if (!validateForm()) return;

    try {
      if (isEditing) {
        // 编辑模式：只发送可编辑的字段，确保布尔类型正确
        await onSave({
          label: formData.label,
          placeholder: formData.placeholder || undefined,
          icon: formData.icon || undefined,
          sort_order: formData.sort_order,
          is_active: Boolean(formData.is_active), // 确保是布尔值
        });
      } else {
        // 新建模式：先验证必填字段
        if (!formData.label || !formData.label.trim()) {
          throw new Error('标签名称不能为空');
        }

        // 构建配置数据，只包含有效字段
        const configData: any = {
          entity_type: entityType,
          language_code: languageCode,
          label: formData.label.trim(),
        };

        // 添加可选字段（只有在有值时才添加）
        if (formData.placeholder && formData.placeholder.trim()) {
          configData.placeholder = formData.placeholder.trim();
        }

        if (formData.icon && formData.icon.trim()) {
          configData.icon = formData.icon.trim();
        }

        if (typeof formData.sort_order === 'number' && formData.sort_order >= 0) {
          configData.sort_order = formData.sort_order;
        }

        if (typeof formData.is_active === 'boolean') {
          configData.is_active = formData.is_active;
        }

        if (typeof formData.is_preset === 'boolean') {
          configData.is_preset = formData.is_preset;
        }

        console.log('TabConfigDialog 发送的配置数据:', configData);
        console.log('字段类型检查:', {
          entity_type: typeof configData.entity_type,
          language_code: typeof configData.language_code,
          label: typeof configData.label,
          placeholder: typeof configData.placeholder,
          icon: typeof configData.icon,
          sort_order: typeof configData.sort_order,
          is_active: typeof configData.is_active,
          is_preset: typeof configData.is_preset,
        });

        await onSave(configData);
      }
      onOpenChange(false);
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            {isEditing ? '编辑标签页' : '新建标签页'}
            {isPreset && (
              <Badge variant="secondary" className="text-xs">
                预设
              </Badge>
            )}
          </DialogTitle>
          <DialogDescription>
            {isEditing 
              ? '修改标签页的显示信息和设置' 
              : '创建新的富文本标签页配置'
            }
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-4 py-4">
          {/* 显示键名 - 编辑时只读 */}
          {isEditing && config && (
            <div className="space-y-2">
              <Label>键名</Label>
              <Input value={config.key} disabled />
              <p className="text-xs text-muted-foreground">
                键名由系统自动生成，不可修改
              </p>
            </div>
          )}

          {/* 标签名称 */}
          <div className="space-y-2">
            <Label htmlFor="label">标签名称 *</Label>
            <Input
              id="label"
              value={formData.label}
              onChange={(e) => setFormData(prev => ({ ...prev, label: e.target.value }))}
              placeholder="例如: 介绍, 亮点"
              className={errors.label ? 'border-red-500' : ''}
            />
            {errors.label && (
              <p className="text-sm text-red-500">{errors.label}</p>
            )}
          </div>

          {/* 占位符 */}
          <div className="space-y-2">
            <Label htmlFor="placeholder">占位符文本</Label>
            <Textarea
              id="placeholder"
              value={formData.placeholder}
              onChange={(e) => setFormData(prev => ({ ...prev, placeholder: e.target.value }))}
              placeholder="输入编辑器的占位符文本..."
              rows={2}
            />
          </div>

          {/* 图标 */}
          <div className="space-y-2">
            <Label htmlFor="icon">图标</Label>
            <Input
              id="icon"
              value={formData.icon}
              onChange={(e) => setFormData(prev => ({ ...prev, icon: e.target.value }))}
              placeholder="例如: info, star, guide"
            />
            <p className="text-xs text-muted-foreground">
              图标名称，用于显示在标签页旁边
            </p>
          </div>

          {/* 排序 */}
          <div className="space-y-2">
            <Label htmlFor="sort_order">排序</Label>
            <Input
              id="sort_order"
              type="number"
              value={formData.sort_order}
              onChange={(e) => setFormData(prev => ({ ...prev, sort_order: parseInt(e.target.value) || 0 }))}
              min="0"
            />
          </div>

          {/* 状态开关 */}
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label>启用状态</Label>
              <p className="text-xs text-muted-foreground">
                禁用后该标签页将不会显示
              </p>
            </div>
            <Switch
              checked={formData.is_active}
              onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_active: checked }))}
            />
          </div>

          {/* 预设标签页 - 仅新建时可设置 */}
          {Boolean(isEditing) === false && (
            <div className="flex items-center justify-between">
              <div className="space-y-0.5">
                <Label>预设标签页</Label>
                <p className="text-xs text-muted-foreground">
                  预设标签页不能被删除
                </p>
              </div>
              <Switch
                checked={formData.is_preset}
                onCheckedChange={(checked) => setFormData(prev => ({ ...prev, is_preset: checked }))}
              />
            </div>
          )}

          {/* 预设标签页警告 */}
          {Boolean(isPreset) && (
            <Alert>
              <AlertCircle className="h-4 w-4" />
              <AlertDescription>
                这是预设标签页，某些设置可能受到限制
              </AlertDescription>
            </Alert>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={() => onOpenChange(false)}>
            取消
          </Button>
          <Button onClick={handleSave} disabled={isLoading}>
            {isLoading && <Loader2 className="mr-2 h-4 w-4 animate-spin" />}
            {isEditing ? '保存' : '创建'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
