'use client';

import React, { useState } from 'react';
import { motion } from 'motion/react';
import {
  Settings,
  Globe,
  Palette,
  Bell,
  Shield,
  Key,
  Volume2,
  VolumeX,
  Save,
  Check
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Switch } from '@/components/ui/switch';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Separator } from '@/components/ui/separator';
import { Badge } from '@/components/ui/badge';
import { useLocale } from 'next-intl';

export function UserSettings() {
  const locale = useLocale();
  const [settings, setSettings] = useState({
    language: locale,
    notifications: {
      email: true,
      push: false,
      sound: true,
      newEvents: true,
      bookmarkUpdates: false,
    },
    privacy: {
      profileVisible: true,
      activityVisible: false,
      bookmarksVisible: true,
    },
  });

  const [hasChanges, setHasChanges] = useState(false);
  const [isSaving, setIsSaving] = useState(false);

  const handleSettingChange = (category: string, key: string, value: any) => {
    setSettings(prev => ({
      ...prev,
      [category]: typeof prev[category] === 'object' 
        ? { ...prev[category], [key]: value }
        : value
    }));
    setHasChanges(true);
  };

  const handleSave = async () => {
    setIsSaving(true);
    // TODO: 实现保存设置的API调用
    await new Promise(resolve => setTimeout(resolve, 1000));
    setIsSaving(false);
    setHasChanges(false);
  };

  const settingSections = [
    {
      id: 'appearance',
      title: '外观设置',
      description: '自定义界面外观和主题',
      icon: Palette,
      items: [
        {
          id: 'language',
          label: '语言',
          description: '选择界面显示语言',
          type: 'select',
          value: settings.language,
          options: [
            { value: 'zh', label: '中文' },
            { value: 'ja', label: '日本語' },
            { value: 'en', label: 'English' },
          ],
          onChange: (value: string) => handleSettingChange('language', '', value),
        },
      ],
    },
    {
      id: 'notifications',
      title: '通知设置',
      description: '管理通知偏好和提醒方式',
      icon: Bell,
      items: [
        {
          id: 'email',
          label: '邮件通知',
          description: '接收重要更新的邮件通知',
          type: 'switch',
          value: settings.notifications.email,
          onChange: (value: boolean) => handleSettingChange('notifications', 'email', value),
        },
        {
          id: 'push',
          label: '推送通知',
          description: '接收浏览器推送通知',
          type: 'switch',
          value: settings.notifications.push,
          onChange: (value: boolean) => handleSettingChange('notifications', 'push', value),
        },
        {
          id: 'sound',
          label: '声音提醒',
          description: '通知时播放提示音',
          type: 'switch',
          value: settings.notifications.sound,
          onChange: (value: boolean) => handleSettingChange('notifications', 'sound', value),
        },
        {
          id: 'newEvents',
          label: '新事件通知',
          description: '有新事件发布时通知我',
          type: 'switch',
          value: settings.notifications.newEvents,
          onChange: (value: boolean) => handleSettingChange('notifications', 'newEvents', value),
        },
        {
          id: 'bookmarkUpdates',
          label: '收藏更新',
          description: '收藏的内容有更新时通知我',
          type: 'switch',
          value: settings.notifications.bookmarkUpdates,
          onChange: (value: boolean) => handleSettingChange('notifications', 'bookmarkUpdates', value),
        },
      ],
    },
    {
      id: 'privacy',
      title: '隐私设置',
      description: '控制个人信息的可见性',
      icon: Shield,
      items: [
        {
          id: 'profileVisible',
          label: '公开个人资料',
          description: '允许其他用户查看我的个人资料',
          type: 'switch',
          value: settings.privacy.profileVisible,
          onChange: (value: boolean) => handleSettingChange('privacy', 'profileVisible', value),
        },
        {
          id: 'activityVisible',
          label: '公开活动记录',
          description: '允许其他用户查看我的活动记录',
          type: 'switch',
          value: settings.privacy.activityVisible,
          onChange: (value: boolean) => handleSettingChange('privacy', 'activityVisible', value),
        },
        {
          id: 'bookmarksVisible',
          label: '公开收藏列表',
          description: '允许其他用户查看我的收藏',
          type: 'switch',
          value: settings.privacy.bookmarksVisible,
          onChange: (value: boolean) => handleSettingChange('privacy', 'bookmarksVisible', value),
        },
      ],
    },
  ];

  return (
    <div className="space-y-6">
      {/* Page Header */}
      <motion.div
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold bg-gradient-to-r from-slate-900 to-slate-600 dark:from-slate-100 dark:to-slate-400 bg-clip-text text-transparent">
              设置
            </h1>
            <p className="text-slate-600 dark:text-slate-400 mt-1">
              个性化您的使用体验
            </p>
          </div>
          {hasChanges && (
            <Button
              onClick={handleSave}
              disabled={isSaving}
              className="gap-2"
            >
              {isSaving ? (
                <>
                  <div className="h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
                  保存中...
                </>
              ) : (
                <>
                  <Save className="h-4 w-4" />
                  保存更改
                </>
              )}
            </Button>
          )}
        </div>
      </motion.div>

      {/* Settings Sections */}
      <div className="space-y-6">
        {settingSections.map((section, sectionIndex) => {
          const SectionIcon = section.icon;
          
          return (
            <motion.div
              key={section.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5, delay: sectionIndex * 0.1 }}
            >
              <Card className="overflow-hidden bg-gradient-to-br from-white to-slate-50 dark:from-slate-900 dark:to-slate-800 border-slate-200/50 dark:border-slate-700/50">
                <CardHeader>
                  <CardTitle className="flex items-center gap-3">
                    <div className="p-2 rounded-lg bg-gradient-to-br from-blue-500 to-purple-600 text-white">
                      <SectionIcon className="h-5 w-5" />
                    </div>
                    <div>
                      <h3 className="text-lg font-semibold">{section.title}</h3>
                      <p className="text-sm text-slate-600 dark:text-slate-400 font-normal">
                        {section.description}
                      </p>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  {section.items.map((item, itemIndex) => (
                    <motion.div
                      key={item.id}
                      className="flex items-center justify-between p-4 rounded-lg bg-slate-50/50 dark:bg-slate-800/30 hover:bg-slate-100/50 dark:hover:bg-slate-800/50 transition-colors"
                      initial={{ opacity: 0, x: -10 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ duration: 0.3, delay: sectionIndex * 0.1 + itemIndex * 0.05 }}
                    >
                      <div className="flex-1">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium text-slate-900 dark:text-slate-100">
                            {item.label}
                          </h4>
                          {item.type === 'switch' && item.value && (
                            <Badge variant="secondary" className="text-xs">
                              <Check className="h-3 w-3 mr-1" />
                              启用
                            </Badge>
                          )}
                        </div>
                        <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                          {item.description}
                        </p>
                      </div>
                      
                      <div className="ml-4">
                        {item.type === 'switch' && (
                          <Switch
                            checked={item.value}
                            onCheckedChange={item.onChange}
                          />
                        )}
                        
                        {item.type === 'select' && (
                          <Select value={item.value} onValueChange={item.onChange}>
                            <SelectTrigger className="w-32">
                              <SelectValue />
                            </SelectTrigger>
                            <SelectContent>
                              {item.options?.map((option) => {
                                const OptionIcon = option.icon;
                                return (
                                  <SelectItem key={option.value} value={option.value}>
                                    <div className="flex items-center gap-2">
                                      {OptionIcon && <OptionIcon className="h-4 w-4" />}
                                      {option.label}
                                    </div>
                                  </SelectItem>
                                );
                              })}
                            </SelectContent>
                          </Select>
                        )}
                      </div>
                    </motion.div>
                  ))}
                </CardContent>
              </Card>
            </motion.div>
          );
        })}
      </div>

      {/* Account Security Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.4 }}
      >
        <Card className="border-orange-200 dark:border-orange-800 bg-gradient-to-br from-orange-50 to-red-50 dark:from-orange-950 dark:to-red-950">
          <CardHeader>
            <CardTitle className="flex items-center gap-3 text-orange-900 dark:text-orange-100">
              <div className="p-2 rounded-lg bg-gradient-to-br from-orange-500 to-red-600 text-white">
                <Key className="h-5 w-5" />
              </div>
              <div>
                <h3 className="text-lg font-semibold">账户安全</h3>
                <p className="text-sm text-orange-700 dark:text-orange-300 font-normal">
                  管理密码和安全设置
                </p>
              </div>
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between p-4 rounded-lg bg-white/50 dark:bg-slate-800/30">
              <div>
                <h4 className="font-medium text-slate-900 dark:text-slate-100">
                  修改密码
                </h4>
                <p className="text-sm text-slate-600 dark:text-slate-400 mt-1">
                  定期更新密码以保护账户安全
                </p>
              </div>
              <Button variant="outline" size="sm">
                修改密码
              </Button>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  );
}
