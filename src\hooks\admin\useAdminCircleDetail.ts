import { useQuery } from "@tanstack/react-query";

import { queryKeys } from "@/constants/queryKeys";
import { request } from "@/lib/http";

export interface CircleDetail {
  id: string;
  name: string;
  author?: string;
  urls?: string;
}

export function useAdminCircleDetail(id: string | undefined) {
  return useQuery({
    queryKey: queryKeys.adminCircleDetail(id ?? ""),
    enabled: !!id,
    queryFn: () => request<CircleDetail>(`/admin/circles/${id}`),
    staleTime: 1000 * 60 * 5,
  });
} 