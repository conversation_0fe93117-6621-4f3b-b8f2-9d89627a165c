"use client"

import { skipToken, useQueryClient } from "@tanstack/react-query"
import { useParams } from "next/navigation"
import { useEffect, useState, useMemo } from "react"
import { useLocale } from "next-intl"

import {
  useGetEventsId,
  useGetEventsIdCircles,
  useGetAppearances
} from "@/api/generated/ayafeedComponents"
import EnhancedEventHeader from "@/components/events/EnhancedEventHeader"
import EventDetailTabs from "@/components/events/EventDetailTabs"
import { EventHeaderSkeleton } from "@/components/events/EnhancedSkeleton"
import { useDebounce } from "@/hooks"
import { log } from "@/lib/logger"
import {
  transformEventDataWithLocale,
  transformCirclesData,
  filterCircles
} from "./utils"

/**
 * 事件详情页面组件
 *
 * 功能：
 * - 显示事件基本信息
 * - 展示参与社团列表
 * - 支持社团搜索和分类筛选
 * - 支持多语言切换
 */
export default function EventDetailPage() {
  const { id } = useParams<{ id: string }>()
  const locale = useLocale()
  const queryClient = useQueryClient()

  // 搜索和筛选状态
  const [keyword, setKeyword] = useState("")
  const debouncedKeyword = useDebounce(keyword, 300)

  // 获取事件基本信息
  const {
    data: eventData,
    isLoading: isEventLoading,
    error: eventError
  } = useGetEventsId(
    id ? { pathParams: { id } } : skipToken,
    {
      select: (data) => data ? transformEventDataWithLocale(data, locale) : null,
      staleTime: 5 * 60 * 1000, // 5分钟缓存
    }
  )

  // 获取社团列表
  const {
    data: circlesData,
    isLoading: isCirclesLoading
  } = useGetEventsIdCircles(
    id ? { pathParams: { id } } : skipToken,
    {
      enabled: !!id,
      staleTime: 5 * 60 * 1000,
    }
  )

  // 获取参展记录（用于获取 booth_id）
  const {
    data: appearancesData,
    isLoading: isAppearancesLoading
  } = useGetAppearances(
    id ? {
      queryParams: { event_id: id, page: "1", pageSize: "800" }
    } : skipToken,
    {
      select: (data) => data?.items ?? [],
      enabled: !!id,
      staleTime: 5 * 60 * 1000,
    }
  )

  // 数据处理和计算
  const circles = useMemo(() => {
    if (!circlesData || !appearancesData) return []
    return transformCirclesData(circlesData, appearancesData)
  }, [circlesData, appearancesData])

  const filteredCircles = useMemo(() => {
    return filterCircles(circles, debouncedKeyword)
  }, [circles, debouncedKeyword])

  // const total = filteredCircles.length // 移到 EventDetailTabs 组件内部处理

  // 调试用输出
  useEffect(() => {
    if (process.env.NODE_ENV === "development") {
      log("eventData", eventData)
    }
  }, [eventData])

  // 监听语言变化，重新获取数据
  useEffect(() => {
    if (id) {
      queryClient.invalidateQueries({
        predicate: (query) => {
          const queryKey = query.queryKey
          return queryKey.some(key =>
            typeof key === 'string' && key === id ||
            (typeof key === 'object' && key !== null && 'id' in key && (key as any).id === id)
          )
        }
      })
    }
  }, [locale, id, queryClient])



  // 加载状态
  const isLoading = isEventLoading || isCirclesLoading || isAppearancesLoading

  // 错误处理
  if (eventError) {
    return (
      <div className="min-h-screen bg-background text-foreground flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-red-600 mb-2">加载失败</h1>
          <p className="text-gray-600">无法加载事件信息，请稍后重试</p>
        </div>
      </div>
    )
  }

  // 如果事件数据加载中，显示骨架屏
  if (isEventLoading && !eventData) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 relative overflow-hidden">
        {/* Background decorative elements */}
        <div className="absolute inset-0 overflow-hidden pointer-events-none">
          <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-blue-400/10 to-purple-600/10 rounded-full blur-3xl"></div>
          <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-purple-400/10 to-pink-600/10 rounded-full blur-3xl"></div>
        </div>

        <div className="relative">
          <EventHeaderSkeleton />
          <main className="max-w-7xl mx-auto px-4 py-10">
            <div className="space-y-6">
              <div className="skeleton h-10 w-full rounded-lg" />
              <div className="grid grid-cols-[repeat(auto-fill,minmax(220px,1fr))] gap-4">
                {Array.from({ length: 12 }).map((_, i) => (
                  <div key={i} className="skeleton h-32 rounded-lg" />
                ))}
              </div>
            </div>
          </main>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen news-gradient-bg vintage-paper-texture relative overflow-hidden">
      {/* 老式报纸主题装饰元素 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-amber-400/8 to-orange-600/8 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-yellow-400/8 to-amber-600/8 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-orange-400/5 to-yellow-600/5 rounded-full blur-3xl"></div>

        {/* 新闻主题装饰线条 */}
        <div className="absolute top-20 right-20 w-32 h-px bg-gradient-to-r from-transparent via-amber-400/20 to-transparent"></div>
        <div className="absolute bottom-32 left-32 w-48 h-px bg-gradient-to-r from-transparent via-orange-400/20 to-transparent"></div>
        <div className="absolute top-1/3 left-20 w-24 h-px bg-gradient-to-r from-transparent via-yellow-400/20 to-transparent rotate-45"></div>
      </div>

      <div className="relative">
        {/* 增强版页面头部 */}
        <EnhancedEventHeader
          event={eventData ?? null}
          circlesCount={circles.length}
        />

        {/* 主体内容 - 使用标签页布局 */}
        <main className="max-w-7xl mx-auto px-4 py-10 vintage-paper-texture">
          <EventDetailTabs
            event={eventData ?? null}
            circles={circles}
            filteredCircles={filteredCircles}
            keyword={keyword}
            setKeyword={setKeyword}
            isLoading={isLoading}
          />
        </main>
      </div>
    </div>
  )
}
