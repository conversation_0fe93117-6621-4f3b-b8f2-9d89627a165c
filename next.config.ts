import type { NextConfig } from "next";
import bundleAnalyzer from '@next/bundle-analyzer'
import createNextIntlPlugin from 'next-intl/plugin'

// 插件实例
const withBundleAnalyzer = bundleAnalyzer({
  enabled: process.env.ANALYZE === 'true',
})
const withNextIntl = createNextIntlPlugin()

const nextConfig: NextConfig = {
  /* config options here */
  images: {
    remotePatterns: [
      // 本地开发环境
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'localhost',
        port: '',
        pathname: '/**',
      },
      // 本地 API 服务器 (端口 8787)
      {
        protocol: 'http',
        hostname: 'localhost',
        port: '8787',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'localhost',
        port: '8787',
        pathname: '/**',
      },
      // API 服务器
      {
        protocol: 'https',
        hostname: 'api.ayafeed.com',
        pathname: '/**',
      },
      // CDN 服务器
      {
        protocol: 'https',
        hostname: 'images.ayafeed.com',
        pathname: '/**',
      },
      {
        protocol: 'https',
        hostname: 'cdn.ayafeed.com',
        pathname: '/**',
      },
      // Unsplash (用于设计展示)
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
        pathname: '/**',
      },

    ],
    formats: ['image/webp', 'image/avif'],
    deviceSizes: [640, 750, 828, 1080, 1200, 1920],
    imageSizes: [16, 32, 48, 64, 96, 128, 256, 384],
  },
}

// 通过数组 reduce 链式应用插件，避免深层嵌套
const plugins = [withBundleAnalyzer, withNextIntl]

export default plugins.reduce((config, plugin) => plugin(config), nextConfig)
