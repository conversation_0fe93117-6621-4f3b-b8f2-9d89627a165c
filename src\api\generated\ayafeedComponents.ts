/**
 * Generated by @openapi-codegen
 *
 * @version 0.4.2.5
 */
import * as reactQuery from "@tanstack/react-query";
import {
  useAyafeedContext,
  AyafeedContext,
  queryKeyFn,
} from "./ayafeedContext";
import { deepMerge } from "./ayafeedUtils";
import type * as Fetcher from "./ayafeedFetcher";
import { ayafeedFetch } from "./ayafeedFetcher";
import type * as Schemas from "./ayafeedSchemas";

type QueryFnOptions = {
  signal?: AbortController["signal"];
};

export type PostAuthSignupEmailError = Fetcher.ErrorWrapper<
  | {
      status: 400;
      payload: Schemas.ErrorResponse;
    }
  | {
      status: 409;
      payload: {
        /**
         * @example Email already exists
         */
        error: string;
      };
    }
>;

export type PostAuthSignupEmailResponse = {
  user: {
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @format email
     * @example <EMAIL>
     */
    email: string;
    /**
     * @example Alice
     */
    name: string;
    /**
     * @example alice_user
     */
    username?: string;
    /**
     * @example user
     */
    role: "admin" | "editor" | "viewer" | "user";
    /**
     * @example false
     */
    emailVerified: boolean;
    /**
     * @format date-time
     * @example 2024-01-01T00:00:00.000Z
     */
    createdAt: string;
    /**
     * @format date-time
     * @example 2024-01-01T00:00:00.000Z
     */
    updatedAt: string;
  };
  session: {
    /**
     * @example session-123
     */
    id: string;
    /**
     * @example uuid-123
     */
    userId: string;
    /**
     * @format date-time
     * @example 2024-02-01T00:00:00.000Z
     */
    expiresAt: string;
    /**
     * @example ***********
     */
    ipAddress?: string;
    /**
     * @example Mozilla/5.0...
     */
    userAgent?: string;
  };
};

export type PostAuthSignupEmailRequestBody = {
  /**
   * User email address
   *
   * @format email
   * @example <EMAIL>
   */
  email: string;
  /**
   * User password (minimum 8 characters)
   *
   * @minLength 8
   * @example password123
   */
  password: string;
  /**
   * User display name
   *
   * @example Alice
   */
  name?: string;
  /**
   * Unique username (3-30 characters)
   *
   * @example alice_user
   */
  username?: string;
};

export type PostAuthSignupEmailVariables = {
  body: PostAuthSignupEmailRequestBody;
} & AyafeedContext["fetcherOptions"];

export const fetchPostAuthSignupEmail = (
  variables: PostAuthSignupEmailVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostAuthSignupEmailResponse,
    PostAuthSignupEmailError,
    PostAuthSignupEmailRequestBody,
    {},
    {},
    {}
  >({ url: "/auth/sign-up/email", method: "post", ...variables, signal });

export const usePostAuthSignupEmail = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostAuthSignupEmailResponse,
      PostAuthSignupEmailError,
      PostAuthSignupEmailVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostAuthSignupEmailResponse,
    PostAuthSignupEmailError,
    PostAuthSignupEmailVariables
  >({
    mutationFn: (variables: PostAuthSignupEmailVariables) =>
      fetchPostAuthSignupEmail(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type PostAuthSigninEmailError = Fetcher.ErrorWrapper<
  | {
      status: 400;
      payload: Schemas.ErrorResponse;
    }
  | {
      status: 401;
      payload: {
        /**
         * @example Invalid email or password
         */
        error: string;
      };
    }
>;

export type PostAuthSigninEmailResponse = {
  user: {
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @format email
     * @example <EMAIL>
     */
    email: string;
    /**
     * @example Alice
     */
    name: string;
    /**
     * @example alice_user
     */
    username?: string;
    /**
     * @example user
     */
    role: "admin" | "editor" | "viewer" | "user";
    /**
     * @example false
     */
    emailVerified: boolean;
    /**
     * @format date-time
     * @example 2024-01-01T00:00:00.000Z
     */
    createdAt: string;
    /**
     * @format date-time
     * @example 2024-01-01T00:00:00.000Z
     */
    updatedAt: string;
  };
  session: {
    /**
     * @example session-123
     */
    id: string;
    /**
     * @example uuid-123
     */
    userId: string;
    /**
     * @format date-time
     * @example 2024-02-01T00:00:00.000Z
     */
    expiresAt: string;
    /**
     * @example ***********
     */
    ipAddress?: string;
    /**
     * @example Mozilla/5.0...
     */
    userAgent?: string;
  };
};

export type PostAuthSigninEmailRequestBody = {
  /**
   * User email address
   *
   * @format email
   * @example <EMAIL>
   */
  email: string;
  /**
   * User password
   *
   * @example password123
   */
  password: string;
};

export type PostAuthSigninEmailVariables = {
  body: PostAuthSigninEmailRequestBody;
} & AyafeedContext["fetcherOptions"];

export const fetchPostAuthSigninEmail = (
  variables: PostAuthSigninEmailVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostAuthSigninEmailResponse,
    PostAuthSigninEmailError,
    PostAuthSigninEmailRequestBody,
    {},
    {},
    {}
  >({ url: "/auth/sign-in/email", method: "post", ...variables, signal });

export const usePostAuthSigninEmail = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostAuthSigninEmailResponse,
      PostAuthSigninEmailError,
      PostAuthSigninEmailVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostAuthSigninEmailResponse,
    PostAuthSigninEmailError,
    PostAuthSigninEmailVariables
  >({
    mutationFn: (variables: PostAuthSigninEmailVariables) =>
      fetchPostAuthSigninEmail(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type PostAuthSigninUsernameError = Fetcher.ErrorWrapper<
  | {
      status: 400;
      payload: Schemas.ErrorResponse;
    }
  | {
      status: 401;
      payload: {
        /**
         * @example Invalid username or password
         */
        error: string;
      };
    }
>;

export type PostAuthSigninUsernameResponse = {
  user: {
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @format email
     * @example <EMAIL>
     */
    email: string;
    /**
     * @example Alice
     */
    name: string;
    /**
     * @example alice_user
     */
    username?: string;
    /**
     * @example user
     */
    role: "admin" | "editor" | "viewer" | "user";
    /**
     * @example false
     */
    emailVerified: boolean;
    /**
     * @format date-time
     * @example 2024-01-01T00:00:00.000Z
     */
    createdAt: string;
    /**
     * @format date-time
     * @example 2024-01-01T00:00:00.000Z
     */
    updatedAt: string;
  };
  session: {
    /**
     * @example session-123
     */
    id: string;
    /**
     * @example uuid-123
     */
    userId: string;
    /**
     * @format date-time
     * @example 2024-02-01T00:00:00.000Z
     */
    expiresAt: string;
    /**
     * @example ***********
     */
    ipAddress?: string;
    /**
     * @example Mozilla/5.0...
     */
    userAgent?: string;
  };
};

export type PostAuthSigninUsernameRequestBody = {
  /**
   * Username
   *
   * @example alice_user
   */
  username: string;
  /**
   * User password
   *
   * @example password123
   */
  password: string;
};

export type PostAuthSigninUsernameVariables = {
  body: PostAuthSigninUsernameRequestBody;
} & AyafeedContext["fetcherOptions"];

export const fetchPostAuthSigninUsername = (
  variables: PostAuthSigninUsernameVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostAuthSigninUsernameResponse,
    PostAuthSigninUsernameError,
    PostAuthSigninUsernameRequestBody,
    {},
    {},
    {}
  >({ url: "/auth/sign-in/username", method: "post", ...variables, signal });

export const usePostAuthSigninUsername = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostAuthSigninUsernameResponse,
      PostAuthSigninUsernameError,
      PostAuthSigninUsernameVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostAuthSigninUsernameResponse,
    PostAuthSigninUsernameError,
    PostAuthSigninUsernameVariables
  >({
    mutationFn: (variables: PostAuthSigninUsernameVariables) =>
      fetchPostAuthSigninUsername(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type PostAuthSignoutError = Fetcher.ErrorWrapper<{
  status: 400;
  payload: Schemas.ErrorResponse;
}>;

export type PostAuthSignoutResponse = {
  /**
   * @example true
   */
  success: boolean;
};

export type PostAuthSignoutVariables = AyafeedContext["fetcherOptions"];

export const fetchPostAuthSignout = (
  variables: PostAuthSignoutVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<PostAuthSignoutResponse, PostAuthSignoutError, {}, {}, {}, {}>({
    url: "/auth/sign-out",
    method: "post",
    ...variables,
    signal,
  });

export const usePostAuthSignout = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostAuthSignoutResponse,
      PostAuthSignoutError,
      PostAuthSignoutVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostAuthSignoutResponse,
    PostAuthSignoutError,
    PostAuthSignoutVariables
  >({
    mutationFn: (variables: PostAuthSignoutVariables) =>
      fetchPostAuthSignout(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetAuthGetsessionError = Fetcher.ErrorWrapper<{
  status: 401;
  payload: {
    /**
     * @example Not authenticated
     */
    error: string;
  };
}>;

export type GetAuthGetsessionResponse = {
  user: {
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @format email
     * @example <EMAIL>
     */
    email: string;
    /**
     * @example Alice
     */
    name: string;
    /**
     * @example alice_user
     */
    username?: string;
    /**
     * @example user
     */
    role: "admin" | "editor" | "viewer" | "user";
    /**
     * @example false
     */
    emailVerified: boolean;
    /**
     * @format date-time
     * @example 2024-01-01T00:00:00.000Z
     */
    createdAt: string;
    /**
     * @format date-time
     * @example 2024-01-01T00:00:00.000Z
     */
    updatedAt: string;
  };
  session: {
    /**
     * @example session-123
     */
    id: string;
    /**
     * @example uuid-123
     */
    userId: string;
    /**
     * @format date-time
     * @example 2024-02-01T00:00:00.000Z
     */
    expiresAt: string;
    /**
     * @example ***********
     */
    ipAddress?: string;
    /**
     * @example Mozilla/5.0...
     */
    userAgent?: string;
  };
};

export type GetAuthGetsessionVariables = AyafeedContext["fetcherOptions"];

export const fetchGetAuthGetsession = (
  variables: GetAuthGetsessionVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetAuthGetsessionResponse,
    GetAuthGetsessionError,
    undefined,
    {},
    {},
    {}
  >({ url: "/auth/get-session", method: "get", ...variables, signal });

export function getAuthGetsessionQuery(variables: GetAuthGetsessionVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetAuthGetsessionResponse>;
};

export function getAuthGetsessionQuery(
  variables: GetAuthGetsessionVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetAuthGetsessionResponse>)
    | reactQuery.SkipToken;
};

export function getAuthGetsessionQuery(
  variables: GetAuthGetsessionVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/auth/get-session",
      operationId: "getAuthGetsession",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetAuthGetsession(variables, signal),
  };
}

export const useSuspenseGetAuthGetsession = <
  TData = GetAuthGetsessionResponse,
>(
  variables: GetAuthGetsessionVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAuthGetsessionResponse,
      GetAuthGetsessionError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetAuthGetsessionResponse,
    GetAuthGetsessionError,
    TData
  >({
    ...getAuthGetsessionQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAuthGetsession = <TData = GetAuthGetsessionResponse,>(
  variables: GetAuthGetsessionVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAuthGetsessionResponse,
      GetAuthGetsessionError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetAuthGetsessionResponse,
    GetAuthGetsessionError,
    TData
  >({
    ...getAuthGetsessionQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetEventsQueryParams = {
  /**
   * @example 1
   */
  page?: string;
  /**
   * @example 50
   */
  pageSize?: string;
  /**
   * @example Reitaisai
   */
  keyword?: string;
  /**
   * @example 20250101
   */
  date_from?: string;
  /**
   * @example 20251231
   */
  date_to?: string;
};

export type GetEventsError = Fetcher.ErrorWrapper<undefined>;

export type GetEventsVariables = {
  queryParams?: GetEventsQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetEvents = (
  variables: GetEventsVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.PaginatedResult,
    GetEventsError,
    undefined,
    {},
    GetEventsQueryParams,
    {}
  >({ url: "/events", method: "get", ...variables, signal });

export function getEventsQuery(variables: GetEventsVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<Schemas.PaginatedResult>;
};

export function getEventsQuery(
  variables: GetEventsVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<Schemas.PaginatedResult>)
    | reactQuery.SkipToken;
};

export function getEventsQuery(
  variables: GetEventsVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/events",
      operationId: "getEvents",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetEvents(variables, signal),
  };
}

export const useSuspenseGetEvents = <TData = Schemas.PaginatedResult,>(
  variables: GetEventsVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<Schemas.PaginatedResult, GetEventsError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    Schemas.PaginatedResult,
    GetEventsError,
    TData
  >({
    ...getEventsQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetEvents = <TData = Schemas.PaginatedResult,>(
  variables: GetEventsVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<Schemas.PaginatedResult, GetEventsError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<Schemas.PaginatedResult, GetEventsError, TData>({
    ...getEventsQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetEventsIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type GetEventsIdError = Fetcher.ErrorWrapper<undefined>;

export type GetEventsIdResponse = {
  /**
   * @example uuid-123
   */
  id: string;
  /**
   * @example Reitaisai 22
   */
  name_en: string;
  /**
   * @example 第二十二回博麗神社例大祭
   */
  name_ja: string;
  /**
   * @example 第二十二回博丽神社例大祭
   */
  name_zh: string;
  /**
   * @example May 3, 2025 (Sat) 10:30 – 15:30
   */
  date_en: string;
  /**
   * @example 2025年5月3日(土・祝) 10:30 – 15:30
   */
  date_ja: string;
  /**
   * @example 2025年5月3日(周六) 10:30 – 15:30
   */
  date_zh: string;
  /**
   * @example 20250503
   */
  date_sort?: number;
  image_url?: string | null;
  /**
   * @example tokyo-big-sight
   */
  venue_id: string;
  url?: string | null;
  created_at?: string;
  updated_at?: string;
};

export type GetEventsIdVariables = {
  pathParams: GetEventsIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetEventsId = (
  variables: GetEventsIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetEventsIdResponse,
    GetEventsIdError,
    undefined,
    {},
    {},
    GetEventsIdPathParams
  >({ url: "/events/{id}", method: "get", ...variables, signal });

export function getEventsIdQuery(variables: GetEventsIdVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetEventsIdResponse>;
};

export function getEventsIdQuery(
  variables: GetEventsIdVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetEventsIdResponse>)
    | reactQuery.SkipToken;
};

export function getEventsIdQuery(
  variables: GetEventsIdVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/events/{id}",
      operationId: "getEventsId",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetEventsId(variables, signal),
  };
}

export const useSuspenseGetEventsId = <TData = GetEventsIdResponse,>(
  variables: GetEventsIdVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<GetEventsIdResponse, GetEventsIdError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetEventsIdResponse,
    GetEventsIdError,
    TData
  >({
    ...getEventsIdQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetEventsId = <TData = GetEventsIdResponse,>(
  variables: GetEventsIdVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<GetEventsIdResponse, GetEventsIdError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<GetEventsIdResponse, GetEventsIdError, TData>({
    ...getEventsIdQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetEventsIdCirclesPathParams = {
  id: string;
};

export type GetEventsIdCirclesError = Fetcher.ErrorWrapper<undefined>;

export type GetEventsIdCirclesResponse = {
  /**
   * @example uuid-123
   */
  id: string;
  /**
   * @example Reitaisai 22
   */
  name_en: string;
  /**
   * @example 第二十二回博麗神社例大祭
   */
  name_ja: string;
  /**
   * @example 第二十二回博丽神社例大祭
   */
  name_zh: string;
  /**
   * @example May 3, 2025 (Sat) 10:30 – 15:30
   */
  date_en: string;
  /**
   * @example 2025年5月3日(土・祝) 10:30 – 15:30
   */
  date_ja: string;
  /**
   * @example 2025年5月3日(周六) 10:30 – 15:30
   */
  date_zh: string;
  /**
   * @example 20250503
   */
  date_sort?: number;
  image_url?: string | null;
  /**
   * @example tokyo-big-sight
   */
  venue_id: string;
  url?: string | null;
  created_at?: string;
  updated_at?: string;
  /**
   * @example あ01a
   */
  booth_id?: string | null;
}[];

export type GetEventsIdCirclesVariables = {
  pathParams: GetEventsIdCirclesPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetEventsIdCircles = (
  variables: GetEventsIdCirclesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetEventsIdCirclesResponse,
    GetEventsIdCirclesError,
    undefined,
    {},
    {},
    GetEventsIdCirclesPathParams
  >({ url: "/events/{id}/circles", method: "get", ...variables, signal });

export function getEventsIdCirclesQuery(
  variables: GetEventsIdCirclesVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetEventsIdCirclesResponse>;
};

export function getEventsIdCirclesQuery(
  variables: GetEventsIdCirclesVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetEventsIdCirclesResponse>)
    | reactQuery.SkipToken;
};

export function getEventsIdCirclesQuery(
  variables: GetEventsIdCirclesVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/events/{id}/circles",
      operationId: "getEventsIdCircles",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetEventsIdCircles(variables, signal),
  };
}

export const useSuspenseGetEventsIdCircles = <
  TData = GetEventsIdCirclesResponse,
>(
  variables: GetEventsIdCirclesVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetEventsIdCirclesResponse,
      GetEventsIdCirclesError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetEventsIdCirclesResponse,
    GetEventsIdCirclesError,
    TData
  >({
    ...getEventsIdCirclesQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetEventsIdCircles = <TData = GetEventsIdCirclesResponse,>(
  variables: GetEventsIdCirclesVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetEventsIdCirclesResponse,
      GetEventsIdCirclesError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetEventsIdCirclesResponse,
    GetEventsIdCirclesError,
    TData
  >({
    ...getEventsIdCirclesQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetEventsIdAppearancesPathParams = {
  id: string;
};

export type GetEventsIdAppearancesError = Fetcher.ErrorWrapper<undefined>;

export type GetEventsIdAppearancesResponse = {
  /**
   * @example 120
   */
  total: number;
  /**
   * @example 1
   */
  page: number;
  /**
   * @example 20
   */
  pageSize: number;
  items: ({
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @example Reitaisai 22
     */
    name_en: string;
    /**
     * @example 第二十二回博麗神社例大祭
     */
    name_ja: string;
    /**
     * @example 第二十二回博丽神社例大祭
     */
    name_zh: string;
    /**
     * @example May 3, 2025 (Sat) 10:30 – 15:30
     */
    date_en: string;
    /**
     * @example 2025年5月3日(土・祝) 10:30 – 15:30
     */
    date_ja: string;
    /**
     * @example 2025年5月3日(周六) 10:30 – 15:30
     */
    date_zh: string;
    /**
     * @example 20250503
     */
    date_sort?: number;
    image_url?: string | null;
    /**
     * @example tokyo-big-sight
     */
    venue_id: string;
    url?: string | null;
    created_at?: string;
    updated_at?: string;
  } | null)[];
};

export type GetEventsIdAppearancesVariables = {
  pathParams: GetEventsIdAppearancesPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetEventsIdAppearances = (
  variables: GetEventsIdAppearancesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetEventsIdAppearancesResponse,
    GetEventsIdAppearancesError,
    undefined,
    {},
    {},
    GetEventsIdAppearancesPathParams
  >({ url: "/events/{id}/appearances", method: "get", ...variables, signal });

export function getEventsIdAppearancesQuery(
  variables: GetEventsIdAppearancesVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetEventsIdAppearancesResponse>;
};

export function getEventsIdAppearancesQuery(
  variables: GetEventsIdAppearancesVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetEventsIdAppearancesResponse>)
    | reactQuery.SkipToken;
};

export function getEventsIdAppearancesQuery(
  variables: GetEventsIdAppearancesVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/events/{id}/appearances",
      operationId: "getEventsIdAppearances",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetEventsIdAppearances(variables, signal),
  };
}

export const useSuspenseGetEventsIdAppearances = <
  TData = GetEventsIdAppearancesResponse,
>(
  variables: GetEventsIdAppearancesVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetEventsIdAppearancesResponse,
      GetEventsIdAppearancesError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetEventsIdAppearancesResponse,
    GetEventsIdAppearancesError,
    TData
  >({
    ...getEventsIdAppearancesQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetEventsIdAppearances = <
  TData = GetEventsIdAppearancesResponse,
>(
  variables: GetEventsIdAppearancesVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetEventsIdAppearancesResponse,
      GetEventsIdAppearancesError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetEventsIdAppearancesResponse,
    GetEventsIdAppearancesError,
    TData
  >({
    ...getEventsIdAppearancesQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetVenuesQueryParams = {
  /**
   * @example 1
   */
  page?: string;
  /**
   * @example 50
   */
  pageSize?: string;
  /**
   * @example Big Sight
   */
  keyword?: string;
  /**
   * @example Tokyo
   */
  city?: string;
  /**
   * @example 1000
   */
  capacity_min?: string;
  /**
   * @example 10000
   */
  capacity_max?: string;
  /**
   * @example true
   */
  has_parking?: string;
  /**
   * @example true
   */
  has_wifi?: string;
};

export type GetVenuesError = Fetcher.ErrorWrapper<undefined>;

export type GetVenuesResponse = {
  /**
   * @example 120
   */
  total: number;
  /**
   * @example 1
   */
  page: number;
  /**
   * @example 20
   */
  pageSize: number;
  items: {
    /**
     * @example tokyo-big-sight
     */
    id: string;
    /**
     * @example Reitaisai 22
     */
    name_en?: string;
    /**
     * @example 第二十二回博麗神社例大祭
     */
    name_ja?: string;
    /**
     * @example 第二十二回博丽神社例大祭
     */
    name_zh?: string;
    /**
     * @example May 3, 2025 (Sat) 10:30 – 15:30
     */
    date_en: string;
    /**
     * @example 2025年5月3日(土・祝) 10:30 – 15:30
     */
    date_ja: string;
    /**
     * @example 2025年5月3日(周六) 10:30 – 15:30
     */
    date_zh: string;
    /**
     * @example 20250503
     */
    date_sort?: number;
    image_url?: string | null;
    /**
     * @example tokyo-big-sight
     */
    venue_id: string;
    url?: string | null;
    created_at?: string;
    updated_at?: string;
    /**
     * @example Tokyo Big Sight
     */
    name: string;
    address?: string | null;
    description?: string | null;
    /**
     * @example 35.6298
     */
    lat: number;
    /**
     * @example 139.793
     */
    lng: number;
    capacity?: number | null;
    website_url?: string | null;
    phone?: string | null;
    facilities?: string | null;
    transportation?: string | null;
    parking_info?: string | null;
  }[];
};

export type GetVenuesVariables = {
  queryParams?: GetVenuesQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetVenues = (
  variables: GetVenuesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetVenuesResponse,
    GetVenuesError,
    undefined,
    {},
    GetVenuesQueryParams,
    {}
  >({ url: "/venues", method: "get", ...variables, signal });

export function getVenuesQuery(variables: GetVenuesVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetVenuesResponse>;
};

export function getVenuesQuery(
  variables: GetVenuesVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetVenuesResponse>)
    | reactQuery.SkipToken;
};

export function getVenuesQuery(
  variables: GetVenuesVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/venues",
      operationId: "getVenues",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetVenues(variables, signal),
  };
}

export const useSuspenseGetVenues = <TData = GetVenuesResponse,>(
  variables: GetVenuesVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<GetVenuesResponse, GetVenuesError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<GetVenuesResponse, GetVenuesError, TData>({
    ...getVenuesQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetVenues = <TData = GetVenuesResponse,>(
  variables: GetVenuesVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<GetVenuesResponse, GetVenuesError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<GetVenuesResponse, GetVenuesError, TData>({
    ...getVenuesQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetVenuesIdPathParams = {
  /**
   * @example tokyo-big-sight
   */
  id: string;
};

export type GetVenuesIdError = Fetcher.ErrorWrapper<undefined>;

export type GetVenuesIdResponse = {
  /**
   * @example tokyo-big-sight
   */
  id: string;
  /**
   * @example Tokyo Big Sight
   */
  name: string;
  address?: string | null;
  description?: string | null;
  /**
   * @example 35.6298
   */
  lat: number;
  /**
   * @example 139.793
   */
  lng: number;
  capacity?: number | null;
  website_url?: string | null;
  phone?: string | null;
  facilities?: string | null;
  transportation?: string | null;
  parking_info?: string | null;
  created_at?: string;
  updated_at?: string;
};

export type GetVenuesIdVariables = {
  pathParams: GetVenuesIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetVenuesId = (
  variables: GetVenuesIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetVenuesIdResponse,
    GetVenuesIdError,
    undefined,
    {},
    {},
    GetVenuesIdPathParams
  >({ url: "/venues/{id}", method: "get", ...variables, signal });

export function getVenuesIdQuery(variables: GetVenuesIdVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetVenuesIdResponse>;
};

export function getVenuesIdQuery(
  variables: GetVenuesIdVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetVenuesIdResponse>)
    | reactQuery.SkipToken;
};

export function getVenuesIdQuery(
  variables: GetVenuesIdVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/venues/{id}",
      operationId: "getVenuesId",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetVenuesId(variables, signal),
  };
}

export const useSuspenseGetVenuesId = <TData = GetVenuesIdResponse,>(
  variables: GetVenuesIdVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<GetVenuesIdResponse, GetVenuesIdError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetVenuesIdResponse,
    GetVenuesIdError,
    TData
  >({
    ...getVenuesIdQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetVenuesId = <TData = GetVenuesIdResponse,>(
  variables: GetVenuesIdVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<GetVenuesIdResponse, GetVenuesIdError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<GetVenuesIdResponse, GetVenuesIdError, TData>({
    ...getVenuesIdQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetCirclesQueryParams = {
  /**
   * @example 1
   */
  page?: string;
  /**
   * @example 50
   */
  pageSize?: string;
  /**
   * @example 東方
   */
  search?: string;
};

export type GetCirclesError = Fetcher.ErrorWrapper<undefined>;

export type GetCirclesResponse = {
  /**
   * @example 120
   */
  total: number;
  /**
   * @example 1
   */
  page: number;
  /**
   * @example 20
   */
  pageSize: number;
  items: {
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @example Reitaisai 22
     */
    name_en?: string;
    /**
     * @example 第二十二回博麗神社例大祭
     */
    name_ja: string;
    /**
     * @example 第二十二回博丽神社例大祭
     */
    name_zh: string;
    /**
     * @example May 3, 2025 (Sat) 10:30 – 15:30
     */
    date_en: string;
    /**
     * @example 2025年5月3日(土・祝) 10:30 – 15:30
     */
    date_ja: string;
    /**
     * @example 2025年5月3日(周六) 10:30 – 15:30
     */
    date_zh: string;
    /**
     * @example 20250503
     */
    date_sort?: number;
    image_url?: string | null;
    /**
     * @example tokyo-big-sight
     */
    venue_id: string;
    url?: string | null;
    /**
     * @example 2024-01-01T00:00:00Z
     */
    created_at?: string;
    /**
     * @example 2024-01-01T00:00:00Z
     */
    updated_at?: string;
    /**
     * @minLength 0
     * @example 東方愛好会
     */
    name: string;
    /**
     * @example {"author":"Alice","twitter_url":"https://twitter.com/example"}
     */
    urls?: string | null;
  }[];
};

export type GetCirclesVariables = {
  queryParams?: GetCirclesQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetCircles = (
  variables: GetCirclesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetCirclesResponse,
    GetCirclesError,
    undefined,
    {},
    GetCirclesQueryParams,
    {}
  >({ url: "/circles", method: "get", ...variables, signal });

export function getCirclesQuery(variables: GetCirclesVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetCirclesResponse>;
};

export function getCirclesQuery(
  variables: GetCirclesVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetCirclesResponse>)
    | reactQuery.SkipToken;
};

export function getCirclesQuery(
  variables: GetCirclesVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/circles",
      operationId: "getCircles",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetCircles(variables, signal),
  };
}

export const useSuspenseGetCircles = <TData = GetCirclesResponse,>(
  variables: GetCirclesVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<GetCirclesResponse, GetCirclesError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetCirclesResponse,
    GetCirclesError,
    TData
  >({
    ...getCirclesQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetCircles = <TData = GetCirclesResponse,>(
  variables: GetCirclesVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<GetCirclesResponse, GetCirclesError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<GetCirclesResponse, GetCirclesError, TData>({
    ...getCirclesQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetCirclesIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type GetCirclesIdError = Fetcher.ErrorWrapper<undefined>;

export type GetCirclesIdResponse = {
  /**
   * @example uuid-123
   */
  id: string;
  /**
   * @minLength 0
   * @example 東方愛好会
   */
  name: string;
  /**
   * @example {"author":"Alice","twitter_url":"https://twitter.com/example"}
   */
  urls?: string | null;
  /**
   * @example 2024-01-01T00:00:00Z
   */
  created_at?: string;
  /**
   * @example 2024-01-01T00:00:00Z
   */
  updated_at?: string;
};

export type GetCirclesIdVariables = {
  pathParams: GetCirclesIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetCirclesId = (
  variables: GetCirclesIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetCirclesIdResponse,
    GetCirclesIdError,
    undefined,
    {},
    {},
    GetCirclesIdPathParams
  >({ url: "/circles/{id}", method: "get", ...variables, signal });

export function getCirclesIdQuery(variables: GetCirclesIdVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetCirclesIdResponse>;
};

export function getCirclesIdQuery(
  variables: GetCirclesIdVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetCirclesIdResponse>)
    | reactQuery.SkipToken;
};

export function getCirclesIdQuery(
  variables: GetCirclesIdVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/circles/{id}",
      operationId: "getCirclesId",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetCirclesId(variables, signal),
  };
}

export const useSuspenseGetCirclesId = <TData = GetCirclesIdResponse,>(
  variables: GetCirclesIdVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<GetCirclesIdResponse, GetCirclesIdError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetCirclesIdResponse,
    GetCirclesIdError,
    TData
  >({
    ...getCirclesIdQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetCirclesId = <TData = GetCirclesIdResponse,>(
  variables: GetCirclesIdVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<GetCirclesIdResponse, GetCirclesIdError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<GetCirclesIdResponse, GetCirclesIdError, TData>({
    ...getCirclesIdQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetCirclesIdAppearancesPathParams = {
  id: string;
};

export type GetCirclesIdAppearancesQueryParams = {
  /**
   * @example 1
   */
  page?: string;
  /**
   * @example 50
   */
  pageSize?: string;
};

export type GetCirclesIdAppearancesError = Fetcher.ErrorWrapper<undefined>;

export type GetCirclesIdAppearancesResponse = {
  /**
   * @example 120
   */
  total: number;
  /**
   * @example 1
   */
  page: number;
  /**
   * @example 20
   */
  pageSize: number;
  items: ({
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @example Reitaisai 22
     */
    name_en: string;
    /**
     * @example 第二十二回博麗神社例大祭
     */
    name_ja: string;
    /**
     * @example 第二十二回博丽神社例大祭
     */
    name_zh: string;
    /**
     * @example May 3, 2025 (Sat) 10:30 – 15:30
     */
    date_en: string;
    /**
     * @example 2025年5月3日(土・祝) 10:30 – 15:30
     */
    date_ja: string;
    /**
     * @example 2025年5月3日(周六) 10:30 – 15:30
     */
    date_zh: string;
    /**
     * @example 20250503
     */
    date_sort?: number;
    image_url?: string | null;
    /**
     * @example tokyo-big-sight
     */
    venue_id: string;
    url?: string | null;
    created_at?: string;
    updated_at?: string;
  } | null)[];
};

export type GetCirclesIdAppearancesVariables = {
  pathParams: GetCirclesIdAppearancesPathParams;
  queryParams?: GetCirclesIdAppearancesQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetCirclesIdAppearances = (
  variables: GetCirclesIdAppearancesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetCirclesIdAppearancesResponse,
    GetCirclesIdAppearancesError,
    undefined,
    {},
    GetCirclesIdAppearancesQueryParams,
    GetCirclesIdAppearancesPathParams
  >({ url: "/circles/{id}/appearances", method: "get", ...variables, signal });

export function getCirclesIdAppearancesQuery(
  variables: GetCirclesIdAppearancesVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (
    options: QueryFnOptions,
  ) => Promise<GetCirclesIdAppearancesResponse>;
};

export function getCirclesIdAppearancesQuery(
  variables: GetCirclesIdAppearancesVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetCirclesIdAppearancesResponse>)
    | reactQuery.SkipToken;
};

export function getCirclesIdAppearancesQuery(
  variables: GetCirclesIdAppearancesVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/circles/{id}/appearances",
      operationId: "getCirclesIdAppearances",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetCirclesIdAppearances(variables, signal),
  };
}

export const useSuspenseGetCirclesIdAppearances = <
  TData = GetCirclesIdAppearancesResponse,
>(
  variables: GetCirclesIdAppearancesVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetCirclesIdAppearancesResponse,
      GetCirclesIdAppearancesError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetCirclesIdAppearancesResponse,
    GetCirclesIdAppearancesError,
    TData
  >({
    ...getCirclesIdAppearancesQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetCirclesIdAppearances = <
  TData = GetCirclesIdAppearancesResponse,
>(
  variables: GetCirclesIdAppearancesVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetCirclesIdAppearancesResponse,
      GetCirclesIdAppearancesError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetCirclesIdAppearancesResponse,
    GetCirclesIdAppearancesError,
    TData
  >({
    ...getCirclesIdAppearancesQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PostCirclesCircleIdBookmarkPathParams = {
  /**
   * @example circle-uuid
   */
  circleId: string;
};

export type PostCirclesCircleIdBookmarkError = Fetcher.ErrorWrapper<{
  status: 401;
  payload: Schemas.ErrorResponse;
}>;

export type PostCirclesCircleIdBookmarkResponse = {
  code?: 0;
  message: string;
  data: {
    isBookmarked: boolean;
  } | null;
};

export type PostCirclesCircleIdBookmarkVariables = {
  pathParams: PostCirclesCircleIdBookmarkPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchPostCirclesCircleIdBookmark = (
  variables: PostCirclesCircleIdBookmarkVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostCirclesCircleIdBookmarkResponse,
    PostCirclesCircleIdBookmarkError,
    undefined,
    {},
    {},
    PostCirclesCircleIdBookmarkPathParams
  >({
    url: "/circles/{circleId}/bookmark",
    method: "post",
    ...variables,
    signal,
  });

export const usePostCirclesCircleIdBookmark = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostCirclesCircleIdBookmarkResponse,
      PostCirclesCircleIdBookmarkError,
      PostCirclesCircleIdBookmarkVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostCirclesCircleIdBookmarkResponse,
    PostCirclesCircleIdBookmarkError,
    PostCirclesCircleIdBookmarkVariables
  >({
    mutationFn: (variables: PostCirclesCircleIdBookmarkVariables) =>
      fetchPostCirclesCircleIdBookmark(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetCirclesCircleIdBookmarkStatusPathParams = {
  /**
   * @example circle-uuid
   */
  circleId: string;
};

export type GetCirclesCircleIdBookmarkStatusError = Fetcher.ErrorWrapper<{
  status: 401;
  payload: Schemas.ErrorResponse;
}>;

export type GetCirclesCircleIdBookmarkStatusResponse = {
  code?: 0;
  message: string;
  data: {
    /**
     * @example true
     */
    isBookmarked: boolean;
    /**
     * @example bookmark-uuid
     */
    bookmarkId: string | null;
    /**
     * @example 2025-01-01T00:00:00Z
     */
    createdAt: string | null;
  } | null;
};

export type GetCirclesCircleIdBookmarkStatusVariables = {
  pathParams: GetCirclesCircleIdBookmarkStatusPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetCirclesCircleIdBookmarkStatus = (
  variables: GetCirclesCircleIdBookmarkStatusVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetCirclesCircleIdBookmarkStatusResponse,
    GetCirclesCircleIdBookmarkStatusError,
    undefined,
    {},
    {},
    GetCirclesCircleIdBookmarkStatusPathParams
  >({
    url: "/circles/{circleId}/bookmark/status",
    method: "get",
    ...variables,
    signal,
  });

export function getCirclesCircleIdBookmarkStatusQuery(
  variables: GetCirclesCircleIdBookmarkStatusVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (
    options: QueryFnOptions,
  ) => Promise<GetCirclesCircleIdBookmarkStatusResponse>;
};

export function getCirclesCircleIdBookmarkStatusQuery(
  variables: GetCirclesCircleIdBookmarkStatusVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((
        options: QueryFnOptions,
      ) => Promise<GetCirclesCircleIdBookmarkStatusResponse>)
    | reactQuery.SkipToken;
};

export function getCirclesCircleIdBookmarkStatusQuery(
  variables: GetCirclesCircleIdBookmarkStatusVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/circles/{circleId}/bookmark/status",
      operationId: "getCirclesCircleIdBookmarkStatus",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetCirclesCircleIdBookmarkStatus(variables, signal),
  };
}

export const useSuspenseGetCirclesCircleIdBookmarkStatus = <
  TData = GetCirclesCircleIdBookmarkStatusResponse,
>(
  variables: GetCirclesCircleIdBookmarkStatusVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetCirclesCircleIdBookmarkStatusResponse,
      GetCirclesCircleIdBookmarkStatusError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetCirclesCircleIdBookmarkStatusResponse,
    GetCirclesCircleIdBookmarkStatusError,
    TData
  >({
    ...getCirclesCircleIdBookmarkStatusQuery(
      deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export const useGetCirclesCircleIdBookmarkStatus = <
  TData = GetCirclesCircleIdBookmarkStatusResponse,
>(
  variables: GetCirclesCircleIdBookmarkStatusVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetCirclesCircleIdBookmarkStatusResponse,
      GetCirclesCircleIdBookmarkStatusError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetCirclesCircleIdBookmarkStatusResponse,
    GetCirclesCircleIdBookmarkStatusError,
    TData
  >({
    ...getCirclesCircleIdBookmarkStatusQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetUserBookmarksQueryParams = {
  /**
   * @minimum 1
   * @default 1
   * @example 1
   */
  page?: number;
  /**
   * @minimum 1
   * @maximum 100
   * @default 20
   * @example 20
   */
  pageSize?: number;
  /**
   * @example eyJjcmVhdGVkX2F0IjoiMjAyNS0wMS0wMVQwMDowMDowMFoifQ==
   */
  cursor?: string;
  /**
   * @example 工作室
   */
  search?: string;
  /**
   * @default created_at
   * @example created_at
   */
  sortBy?: "created_at" | "circle_name";
  /**
   * @default desc
   * @example desc
   */
  sortOrder?: "asc" | "desc";
};

export type GetUserBookmarksError = Fetcher.ErrorWrapper<{
  status: 401;
  payload: Schemas.ErrorResponse;
}>;

export type GetUserBookmarksResponse = {
  code?: 0;
  message: string;
  data: {
    items: {
      /**
       * @example bookmark-uuid
       */
      id: string;
      /**
       * @example 2025-01-01T00:00:00Z
       */
      created_at: string;
      circle: {
        /**
         * @example circle-uuid
         */
        id: string;
        /**
         * @example 某某工作室
         */
        name: string;
        /**
         * @example {"twitter":"@example"}
         */
        urls: string | null;
        /**
         * @example 2025-01-01T00:00:00Z
         */
        created_at: string;
        /**
         * @example 2025-01-01T00:00:00Z
         */
        updated_at: string;
      };
    }[];
    /**
     * @example 15
     */
    total: number;
    /**
     * @example 1
     */
    page: number;
    /**
     * @example 20
     */
    pageSize: number;
    /**
     * @example 1
     */
    totalPages: number;
    /**
     * 下一页的游标，为null表示没有更多数据
     *
     * @example eyJjcmVhdGVkX2F0IjoiMjAyNS0wMS0wMVQwMDowMDowMFoifQ==
     */
    nextCursor: string | null;
    /**
     * 是否还有更多数据
     *
     * @example true
     */
    hasMore: boolean;
  } | null;
};

export type GetUserBookmarksVariables = {
  queryParams?: GetUserBookmarksQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetUserBookmarks = (
  variables: GetUserBookmarksVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetUserBookmarksResponse,
    GetUserBookmarksError,
    undefined,
    {},
    GetUserBookmarksQueryParams,
    {}
  >({ url: "/user/bookmarks", method: "get", ...variables, signal });

export function getUserBookmarksQuery(variables: GetUserBookmarksVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetUserBookmarksResponse>;
};

export function getUserBookmarksQuery(
  variables: GetUserBookmarksVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetUserBookmarksResponse>)
    | reactQuery.SkipToken;
};

export function getUserBookmarksQuery(
  variables: GetUserBookmarksVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/user/bookmarks",
      operationId: "getUserBookmarks",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetUserBookmarks(variables, signal),
  };
}

export const useSuspenseGetUserBookmarks = <TData = GetUserBookmarksResponse,>(
  variables: GetUserBookmarksVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetUserBookmarksResponse,
      GetUserBookmarksError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetUserBookmarksResponse,
    GetUserBookmarksError,
    TData
  >({
    ...getUserBookmarksQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetUserBookmarks = <TData = GetUserBookmarksResponse,>(
  variables: GetUserBookmarksVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetUserBookmarksResponse,
      GetUserBookmarksError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetUserBookmarksResponse,
    GetUserBookmarksError,
    TData
  >({
    ...getUserBookmarksQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetUserBookmarksStatsQueryParams = {
  /**
   * @example true
   */
  includeIds?: string;
};

export type GetUserBookmarksStatsError = Fetcher.ErrorWrapper<{
  status: 401;
  payload: Schemas.ErrorResponse;
}>;

export type GetUserBookmarksStatsResponse = {
  code?: 0;
  message: string;
  data: {
    /**
     * @example 15
     */
    totalBookmarks: number;
    /**
     * @example 3
     */
    recentBookmarks: number;
    /**
     * @example {"original":8,"derivative":7}
     */
    categoryCounts: {
      [key: string]: number;
    };
    /**
     * 用户收藏的所有社团ID列表，仅在 includeIds=true 时返回，用于前端快速收藏状态检查
     *
     * @example circle-1
     * @example circle-2
     * @example circle-3
     */
    bookmarkedCircleIds?: string[];
  } | null;
};

export type GetUserBookmarksStatsVariables = {
  queryParams?: GetUserBookmarksStatsQueryParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 获取用户收藏统计信息，可选择包含收藏的社团ID列表用于前端批量状态检查优化
 */
export const fetchGetUserBookmarksStats = (
  variables: GetUserBookmarksStatsVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetUserBookmarksStatsResponse,
    GetUserBookmarksStatsError,
    undefined,
    {},
    GetUserBookmarksStatsQueryParams,
    {}
  >({ url: "/user/bookmarks/stats", method: "get", ...variables, signal });

/**
 * 获取用户收藏统计信息，可选择包含收藏的社团ID列表用于前端批量状态检查优化
 */
export function getUserBookmarksStatsQuery(
  variables: GetUserBookmarksStatsVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetUserBookmarksStatsResponse>;
};

export function getUserBookmarksStatsQuery(
  variables: GetUserBookmarksStatsVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetUserBookmarksStatsResponse>)
    | reactQuery.SkipToken;
};

export function getUserBookmarksStatsQuery(
  variables: GetUserBookmarksStatsVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/user/bookmarks/stats",
      operationId: "getUserBookmarksStats",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetUserBookmarksStats(variables, signal),
  };
}

/**
 * 获取用户收藏统计信息，可选择包含收藏的社团ID列表用于前端批量状态检查优化
 */
export const useSuspenseGetUserBookmarksStats = <
  TData = GetUserBookmarksStatsResponse,
>(
  variables: GetUserBookmarksStatsVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetUserBookmarksStatsResponse,
      GetUserBookmarksStatsError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetUserBookmarksStatsResponse,
    GetUserBookmarksStatsError,
    TData
  >({
    ...getUserBookmarksStatsQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

/**
 * 获取用户收藏统计信息，可选择包含收藏的社团ID列表用于前端批量状态检查优化
 */
export const useGetUserBookmarksStats = <
  TData = GetUserBookmarksStatsResponse,
>(
  variables: GetUserBookmarksStatsVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetUserBookmarksStatsResponse,
      GetUserBookmarksStatsError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetUserBookmarksStatsResponse,
    GetUserBookmarksStatsError,
    TData
  >({
    ...getUserBookmarksStatsQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PostUserBookmarksBatchError = Fetcher.ErrorWrapper<
  | {
      status: 400;
      payload: Schemas.ErrorResponse;
    }
  | {
      status: 401;
      payload: Schemas.ErrorResponse;
    }
>;

export type PostUserBookmarksBatchResponse = {
  code?: 0;
  message: string;
  data: {
    /**
     * @example circle-1
     * @example circle-2
     */
    success: string[];
    /**
     * @example {"circleId":"circle-3","reason":"社团不存在"}
     */
    failed: {
      /**
       * @example circle-3
       */
      circleId: string;
      /**
       * @example 社团不存在
       */
      reason: string;
    }[];
    /**
     * @example 3
     */
    total: number;
    /**
     * @example 2
     */
    successCount: number;
    /**
     * @example 1
     */
    failedCount: number;
  } | null;
};

export type PostUserBookmarksBatchRequestBody = {
  /**
   * @example add
   */
  action: "add" | "remove";
  /**
   * @minItems 1
   * @maxItems 50
   * @example circle-1
   * @example circle-2
   * @example circle-3
   */
  circleIds: string[];
};

export type PostUserBookmarksBatchVariables = {
  body: PostUserBookmarksBatchRequestBody;
} & AyafeedContext["fetcherOptions"];

export const fetchPostUserBookmarksBatch = (
  variables: PostUserBookmarksBatchVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostUserBookmarksBatchResponse,
    PostUserBookmarksBatchError,
    PostUserBookmarksBatchRequestBody,
    {},
    {},
    {}
  >({ url: "/user/bookmarks/batch", method: "post", ...variables, signal });

export const usePostUserBookmarksBatch = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostUserBookmarksBatchResponse,
      PostUserBookmarksBatchError,
      PostUserBookmarksBatchVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostUserBookmarksBatchResponse,
    PostUserBookmarksBatchError,
    PostUserBookmarksBatchVariables
  >({
    mutationFn: (variables: PostUserBookmarksBatchVariables) =>
      fetchPostUserBookmarksBatch(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetArtistsQueryParams = {
  /**
   * @example 1
   */
  page?: string;
  /**
   * @example 50
   */
  pageSize?: string;
};

export type GetArtistsError = Fetcher.ErrorWrapper<undefined>;

export type GetArtistsResponse = {
  /**
   * @example 120
   */
  total: number;
  /**
   * @example 1
   */
  page: number;
  /**
   * @example 20
   */
  pageSize: number;
  items: {
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @example Reitaisai 22
     */
    name_en?: string;
    /**
     * @example 第二十二回博麗神社例大祭
     */
    name_ja?: string;
    /**
     * @example 第二十二回博丽神社例大祭
     */
    name_zh?: string;
    /**
     * @example May 3, 2025 (Sat) 10:30 – 15:30
     */
    date_en: string;
    /**
     * @example 2025年5月3日(土・祝) 10:30 – 15:30
     */
    date_ja: string;
    /**
     * @example 2025年5月3日(周六) 10:30 – 15:30
     */
    date_zh: string;
    /**
     * @example 20250503
     */
    date_sort?: number;
    image_url?: string | null;
    /**
     * @example tokyo-big-sight
     */
    venue_id: string;
    url?: string | null;
    /**
     * @example 2024-01-01T00:00:00Z
     */
    created_at: string;
    /**
     * @example 2024-01-01T00:00:00Z
     */
    updated_at: string;
    /**
     * @example Alice
     */
    name: string;
    urls?: string | null;
    description?: string | null;
  }[];
};

export type GetArtistsVariables = {
  queryParams?: GetArtistsQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetArtists = (
  variables: GetArtistsVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetArtistsResponse,
    GetArtistsError,
    undefined,
    {},
    GetArtistsQueryParams,
    {}
  >({ url: "/artists", method: "get", ...variables, signal });

export function getArtistsQuery(variables: GetArtistsVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetArtistsResponse>;
};

export function getArtistsQuery(
  variables: GetArtistsVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetArtistsResponse>)
    | reactQuery.SkipToken;
};

export function getArtistsQuery(
  variables: GetArtistsVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/artists",
      operationId: "getArtists",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetArtists(variables, signal),
  };
}

export const useSuspenseGetArtists = <TData = GetArtistsResponse,>(
  variables: GetArtistsVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<GetArtistsResponse, GetArtistsError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetArtistsResponse,
    GetArtistsError,
    TData
  >({
    ...getArtistsQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetArtists = <TData = GetArtistsResponse,>(
  variables: GetArtistsVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<GetArtistsResponse, GetArtistsError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<GetArtistsResponse, GetArtistsError, TData>({
    ...getArtistsQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetArtistsIdPathParams = {
  id: string;
};

export type GetArtistsIdQueryParams = {
  lang?: string;
};

export type GetArtistsIdError = Fetcher.ErrorWrapper<undefined>;

export type GetArtistsIdResponse = {
  /**
   * @example uuid-123
   */
  id: string;
  /**
   * @example Alice
   */
  name: string;
  urls?: string | null;
  /**
   * @example 2024-01-01T00:00:00Z
   */
  created_at: string;
  /**
   * @example 2024-01-01T00:00:00Z
   */
  updated_at: string;
  description?: string | null;
};

export type GetArtistsIdVariables = {
  pathParams: GetArtistsIdPathParams;
  queryParams?: GetArtistsIdQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetArtistsId = (
  variables: GetArtistsIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetArtistsIdResponse,
    GetArtistsIdError,
    undefined,
    {},
    GetArtistsIdQueryParams,
    GetArtistsIdPathParams
  >({ url: "/artists/{id}", method: "get", ...variables, signal });

export function getArtistsIdQuery(variables: GetArtistsIdVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetArtistsIdResponse>;
};

export function getArtistsIdQuery(
  variables: GetArtistsIdVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetArtistsIdResponse>)
    | reactQuery.SkipToken;
};

export function getArtistsIdQuery(
  variables: GetArtistsIdVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/artists/{id}",
      operationId: "getArtistsId",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetArtistsId(variables, signal),
  };
}

export const useSuspenseGetArtistsId = <TData = GetArtistsIdResponse,>(
  variables: GetArtistsIdVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<GetArtistsIdResponse, GetArtistsIdError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetArtistsIdResponse,
    GetArtistsIdError,
    TData
  >({
    ...getArtistsIdQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetArtistsId = <TData = GetArtistsIdResponse,>(
  variables: GetArtistsIdVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<GetArtistsIdResponse, GetArtistsIdError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<GetArtistsIdResponse, GetArtistsIdError, TData>({
    ...getArtistsIdQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetAppearancesQueryParams = {
  circle_id?: string;
  event_id?: string;
  /**
   * @example 1
   */
  page?: string;
  /**
   * @example 50
   */
  pageSize?: string;
};

export type GetAppearancesError = Fetcher.ErrorWrapper<undefined>;

export type GetAppearancesResponse = {
  /**
   * @example 120
   */
  total: number;
  /**
   * @example 1
   */
  page: number;
  /**
   * @example 20
   */
  pageSize: number;
  items: {
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @example Reitaisai 22
     */
    name_en?: string;
    /**
     * @example 第二十二回博麗神社例大祭
     */
    name_ja?: string;
    /**
     * @example 第二十二回博丽神社例大祭
     */
    name_zh?: string;
    /**
     * @example May 3, 2025 (Sat) 10:30 – 15:30
     */
    date_en?: string;
    /**
     * @example 2025年5月3日(土・祝) 10:30 – 15:30
     */
    date_ja?: string;
    /**
     * @example 2025年5月3日(周六) 10:30 – 15:30
     */
    date_zh: string;
    /**
     * @example 20250503
     */
    date_sort?: number;
    image_url?: string | null;
    /**
     * @example tokyo-big-sight
     */
    venue_id: string;
    url?: string | null;
    /**
     * @example 2024-01-01T00:00:00Z
     */
    created_at: string;
    /**
     * @example 2024-01-01T00:00:00Z
     */
    updated_at: string;
    /**
     * @example circle-uuid
     */
    circle_id: string;
    /**
     * @example event-uuid
     */
    event_id: string;
    /**
     * @example artist-uuid
     */
    artist_id?: string | null;
    /**
     * @example A01a
     */
    booth_id: string;
    /**
     * @example /2025/05/03/A1.jpg
     */
    path?: string | null;
  }[];
};

export type GetAppearancesVariables = {
  queryParams?: GetAppearancesQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetAppearances = (
  variables: GetAppearancesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetAppearancesResponse,
    GetAppearancesError,
    undefined,
    {},
    GetAppearancesQueryParams,
    {}
  >({ url: "/appearances", method: "get", ...variables, signal });

export function getAppearancesQuery(variables: GetAppearancesVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetAppearancesResponse>;
};

export function getAppearancesQuery(
  variables: GetAppearancesVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetAppearancesResponse>)
    | reactQuery.SkipToken;
};

export function getAppearancesQuery(
  variables: GetAppearancesVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/appearances",
      operationId: "getAppearances",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetAppearances(variables, signal),
  };
}

export const useSuspenseGetAppearances = <TData = GetAppearancesResponse,>(
  variables: GetAppearancesVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAppearancesResponse,
      GetAppearancesError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetAppearancesResponse,
    GetAppearancesError,
    TData
  >({
    ...getAppearancesQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAppearances = <TData = GetAppearancesResponse,>(
  variables: GetAppearancesVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAppearancesResponse,
      GetAppearancesError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetAppearancesResponse,
    GetAppearancesError,
    TData
  >({
    ...getAppearancesQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PostAppearancesError = Fetcher.ErrorWrapper<undefined>;

export type PostAppearancesResponse = {
  success: boolean;
};

export type PostAppearancesRequestBody = {
  /**
   * @example uuid-123
   */
  id?: string;
  /**
   * @example circle-uuid
   */
  circle_id?: string;
  /**
   * @example event-uuid
   */
  event_id?: string;
  /**
   * @example artist-uuid
   */
  artist_id?: string | null;
  /**
   * @example A01a
   */
  booth_id?: string;
  /**
   * @example /2025/05/03/A1.jpg
   */
  path?: string | null;
};

export type PostAppearancesVariables = {
  body?: PostAppearancesRequestBody;
} & AyafeedContext["fetcherOptions"];

export const fetchPostAppearances = (
  variables: PostAppearancesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostAppearancesResponse,
    PostAppearancesError,
    PostAppearancesRequestBody,
    {},
    {},
    {}
  >({ url: "/appearances", method: "post", ...variables, signal });

export const usePostAppearances = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostAppearancesResponse,
      PostAppearancesError,
      PostAppearancesVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostAppearancesResponse,
    PostAppearancesError,
    PostAppearancesVariables
  >({
    mutationFn: (variables: PostAppearancesVariables) =>
      fetchPostAppearances(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetAppearancesIdPathParams = {
  id: string;
};

export type GetAppearancesIdError = Fetcher.ErrorWrapper<undefined>;

export type GetAppearancesIdResponse = {
  /**
   * @example uuid-123
   */
  id: string;
  /**
   * @example circle-uuid
   */
  circle_id: string;
  /**
   * @example event-uuid
   */
  event_id: string;
  /**
   * @example artist-uuid
   */
  artist_id?: string | null;
  /**
   * @example A01a
   */
  booth_id: string;
  /**
   * @example /2025/05/03/A1.jpg
   */
  path?: string | null;
  /**
   * @example 2024-01-01T00:00:00Z
   */
  created_at: string;
  /**
   * @example 2024-01-01T00:00:00Z
   */
  updated_at: string;
};

export type GetAppearancesIdVariables = {
  pathParams: GetAppearancesIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetAppearancesId = (
  variables: GetAppearancesIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetAppearancesIdResponse,
    GetAppearancesIdError,
    undefined,
    {},
    {},
    GetAppearancesIdPathParams
  >({ url: "/appearances/{id}", method: "get", ...variables, signal });

export function getAppearancesIdQuery(variables: GetAppearancesIdVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetAppearancesIdResponse>;
};

export function getAppearancesIdQuery(
  variables: GetAppearancesIdVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetAppearancesIdResponse>)
    | reactQuery.SkipToken;
};

export function getAppearancesIdQuery(
  variables: GetAppearancesIdVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/appearances/{id}",
      operationId: "getAppearancesId",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetAppearancesId(variables, signal),
  };
}

export const useSuspenseGetAppearancesId = <TData = GetAppearancesIdResponse,>(
  variables: GetAppearancesIdVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAppearancesIdResponse,
      GetAppearancesIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetAppearancesIdResponse,
    GetAppearancesIdError,
    TData
  >({
    ...getAppearancesIdQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAppearancesId = <TData = GetAppearancesIdResponse,>(
  variables: GetAppearancesIdVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAppearancesIdResponse,
      GetAppearancesIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetAppearancesIdResponse,
    GetAppearancesIdError,
    TData
  >({
    ...getAppearancesIdQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type DeleteAppearancesIdPathParams = {
  id: string;
};

export type DeleteAppearancesIdError = Fetcher.ErrorWrapper<undefined>;

export type DeleteAppearancesIdResponse = {
  success: boolean;
};

export type DeleteAppearancesIdVariables = {
  pathParams: DeleteAppearancesIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchDeleteAppearancesId = (
  variables: DeleteAppearancesIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    DeleteAppearancesIdResponse,
    DeleteAppearancesIdError,
    undefined,
    {},
    {},
    DeleteAppearancesIdPathParams
  >({ url: "/appearances/{id}", method: "delete", ...variables, signal });

export const useDeleteAppearancesId = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      DeleteAppearancesIdResponse,
      DeleteAppearancesIdError,
      DeleteAppearancesIdVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    DeleteAppearancesIdResponse,
    DeleteAppearancesIdError,
    DeleteAppearancesIdVariables
  >({
    mutationFn: (variables: DeleteAppearancesIdVariables) =>
      fetchDeleteAppearancesId(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetSearchQueryParams = {
  /**
   * @example Comiket
   */
  q: string;
  /**
   * @default all
   * @example events
   */
  type?: "all" | "events" | "circles";
  /**
   * @example 1
   */
  page?: string;
  /**
   * @example 20
   */
  limit?: string;
};

export type GetSearchError = Fetcher.ErrorWrapper<undefined>;

export type GetSearchResponse = {
  /**
   * @example true
   */
  success: boolean;
  data: {
    /**
     * 结果类型
     *
     * @example event
     */
    type: "event" | "circle";
    /**
     * 资源ID
     *
     * @example 550e8400-e29b-41d4-a716-446655440000
     */
    id: string;
    /**
     * 名称
     *
     * @example Comiket 103
     */
    name: string;
    /**
     * 描述
     *
     * @example 世界最大的同人志即卖会
     */
    description: string | null;
    /**
     * 场馆名称（仅事件类型）
     *
     * @example 东京国际展示场
     */
    venue_name?: string | null;
    /**
     * 开始时间（仅事件类型）
     *
     * @example 2024-12-30T10:00:00Z
     */
    start_date?: string | null;
    /**
     * 图片URL
     *
     * @example https://example.com/comiket103.jpg
     */
    image_url?: string | null;
    /**
     * 搜索相关性评分
     *
     * @example 0.8567
     */
    rank: number;
  }[];
  /**
   * 响应语言
   *
   * @example zh
   */
  locale: string;
  /**
   * 响应时间戳
   *
   * @example 2024-01-15T10:30:00.000Z
   */
  timestamp: string;
  meta: {
    /**
     * 总结果数
     *
     * @example 15
     */
    total: number;
    /**
     * 搜索关键词
     *
     * @example Comiket
     */
    query: string;
    /**
     * 搜索类型
     *
     * @example events
     */
    type: string;
  };
};

export type GetSearchVariables = {
  queryParams: GetSearchQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetSearch = (
  variables: GetSearchVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetSearchResponse,
    GetSearchError,
    undefined,
    {},
    GetSearchQueryParams,
    {}
  >({ url: "/search", method: "get", ...variables, signal });

export function getSearchQuery(variables: GetSearchVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetSearchResponse>;
};

export function getSearchQuery(
  variables: GetSearchVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetSearchResponse>)
    | reactQuery.SkipToken;
};

export function getSearchQuery(
  variables: GetSearchVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/search",
      operationId: "getSearch",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetSearch(variables, signal),
  };
}

export const useSuspenseGetSearch = <TData = GetSearchResponse,>(
  variables: GetSearchVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<GetSearchResponse, GetSearchError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<GetSearchResponse, GetSearchError, TData>({
    ...getSearchQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetSearch = <TData = GetSearchResponse,>(
  variables: GetSearchVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<GetSearchResponse, GetSearchError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<GetSearchResponse, GetSearchError, TData>({
    ...getSearchQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetFeedQueryParams = {
  /**
   * @default 1
   * @example 1
   */
  page?: string;
  /**
   * @default 20
   * @example 20
   */
  limit?: string;
  /**
   * @default all
   * @example all
   */
  type?: "all" | "events" | "circles";
};

export type GetFeedError = Fetcher.ErrorWrapper<undefined>;

export type GetFeedResponse = {
  /**
   * @example true
   */
  success: boolean;
  data: {
    /**
     * Feed项ID
     *
     * @example feed-001
     */
    id: string;
    /**
     * 内容类型
     *
     * @example event
     */
    type: "event" | "circle";
    content: {
      /**
       * 资源ID
       *
       * @example 550e8400-e29b-41d4-a716-446655440000
       */
      id: string;
      /**
       * 名称
       *
       * @example Comiket 103
       */
      name: string;
      /**
       * 描述
       *
       * @example 世界最大的同人志即卖会
       */
      description: string | null;
      /**
       * 开始时间（仅事件类型）
       *
       * @example 2024-12-30T10:00:00Z
       */
      start_date?: string | null;
      /**
       * 图片URL
       *
       * @example https://example.com/comiket103.jpg
       */
      image_url?: string | null;
    };
    /**
     * 创建时间
     *
     * @example 2024-01-15T08:00:00Z
     */
    created_at: string;
  }[];
  /**
   * 响应语言
   *
   * @example zh
   */
  locale: string;
  /**
   * 响应时间戳
   *
   * @example 2024-01-15T10:30:00.000Z
   */
  timestamp: string;
  meta: {
    /**
     * 总数量
     *
     * @example 200
     */
    total: number;
    /**
     * 当前页码
     *
     * @example 1
     */
    page: number;
    /**
     * 每页数量
     *
     * @example 20
     */
    limit: number;
    /**
     * 是否有更多数据
     *
     * @example true
     */
    hasMore: boolean;
  };
};

export type GetFeedVariables = {
  queryParams?: GetFeedQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetFeed = (
  variables: GetFeedVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetFeedResponse,
    GetFeedError,
    undefined,
    {},
    GetFeedQueryParams,
    {}
  >({ url: "/feed", method: "get", ...variables, signal });

export function getFeedQuery(variables: GetFeedVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetFeedResponse>;
};

export function getFeedQuery(
  variables: GetFeedVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetFeedResponse>)
    | reactQuery.SkipToken;
};

export function getFeedQuery(
  variables: GetFeedVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/feed",
      operationId: "getFeed",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetFeed(variables, signal),
  };
}

export const useSuspenseGetFeed = <TData = GetFeedResponse,>(
  variables: GetFeedVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<GetFeedResponse, GetFeedError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<GetFeedResponse, GetFeedError, TData>({
    ...getFeedQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetFeed = <TData = GetFeedResponse,>(
  variables: GetFeedVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<GetFeedResponse, GetFeedError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<GetFeedResponse, GetFeedError, TData>({
    ...getFeedQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetImagesBatchQueryParams = {
  /**
   * @example event1,event2,event3
   */
  events: string;
  /**
   * @example medium
   */
  variant?: "original" | "large" | "medium" | "thumb";
  /**
   * @example poster
   */
  imageType?: "poster" | "logo" | "banner" | "gallery";
};

export type GetImagesBatchError = Fetcher.ErrorWrapper<undefined>;

export type GetImagesBatchResponse = {
  /**
   * @example 200
   */
  code: number;
  /**
   * @example 批量查询成功
   */
  message: string;
  /**
   * @example {"event1":{"id":"img-456","variant":"medium","file_path":"/images/events/event1/poster_medium.jpg"},"event2":{"id":"img-789","variant":"medium","file_path":"/images/events/event2/poster_medium.jpg"},"event3":null}
   */
  data: {
    [key: string]: {
      /**
       * @example uuid-123
       */
      id: string;
      /**
       * @example group-uuid-456
       */
      group_id: string;
      /**
       * @example event
       */
      resource_type: "event" | "circle" | "venue";
      /**
       * @example resource-uuid-789
       */
      resource_id: string;
      /**
       * @example poster
       */
      image_type: "poster" | "logo" | "banner" | "gallery";
      /**
       * @example thumb
       */
      variant: "original" | "large" | "medium" | "thumb";
      /**
       * @example /images/events/reitaisai-22/poster_thumb.jpg
       */
      file_path: string;
      /**
       * @example 51200
       */
      file_size?: number | null;
      /**
       * @example 200
       */
      width?: number | null;
      /**
       * @example 300
       */
      height?: number | null;
      /**
       * @example jpeg
       */
      format?: string | null;
      /**
       * @example 2025-01-30T00:00:00Z
       */
      created_at?: string;
      /**
       * @example 2025-01-30T00:00:00Z
       */
      updated_at?: string;
    } | null;
  };
};

export type GetImagesBatchVariables = {
  queryParams: GetImagesBatchQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetImagesBatch = (
  variables: GetImagesBatchVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetImagesBatchResponse,
    GetImagesBatchError,
    undefined,
    {},
    GetImagesBatchQueryParams,
    {}
  >({ url: "/images/batch", method: "get", ...variables, signal });

export function getImagesBatchQuery(variables: GetImagesBatchVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetImagesBatchResponse>;
};

export function getImagesBatchQuery(
  variables: GetImagesBatchVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetImagesBatchResponse>)
    | reactQuery.SkipToken;
};

export function getImagesBatchQuery(
  variables: GetImagesBatchVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/images/batch",
      operationId: "getImagesBatch",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetImagesBatch(variables, signal),
  };
}

export const useSuspenseGetImagesBatch = <TData = GetImagesBatchResponse,>(
  variables: GetImagesBatchVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetImagesBatchResponse,
      GetImagesBatchError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetImagesBatchResponse,
    GetImagesBatchError,
    TData
  >({
    ...getImagesBatchQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetImagesBatch = <TData = GetImagesBatchResponse,>(
  variables: GetImagesBatchVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetImagesBatchResponse,
      GetImagesBatchError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetImagesBatchResponse,
    GetImagesBatchError,
    TData
  >({
    ...getImagesBatchQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetImagesIdFilePathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type GetImagesIdFileError = Fetcher.ErrorWrapper<undefined>;

export type GetImagesIdFileVariables = {
  pathParams: GetImagesIdFilePathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetImagesIdFile = (
  variables: GetImagesIdFileVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    undefined,
    GetImagesIdFileError,
    undefined,
    {},
    {},
    GetImagesIdFilePathParams
  >({ url: "/images/{id}/file", method: "get", ...variables, signal });

export function getImagesIdFileQuery(variables: GetImagesIdFileVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<undefined>;
};

export function getImagesIdFileQuery(
  variables: GetImagesIdFileVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<undefined>)
    | reactQuery.SkipToken;
};

export function getImagesIdFileQuery(
  variables: GetImagesIdFileVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/images/{id}/file",
      operationId: "getImagesIdFile",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetImagesIdFile(variables, signal),
  };
}

export const useSuspenseGetImagesIdFile = <TData = undefined,>(
  variables: GetImagesIdFileVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<undefined, GetImagesIdFileError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<undefined, GetImagesIdFileError, TData>({
    ...getImagesIdFileQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetImagesIdFile = <TData = undefined,>(
  variables: GetImagesIdFileVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<undefined, GetImagesIdFileError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<undefined, GetImagesIdFileError, TData>({
    ...getImagesIdFileQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetImagesIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type GetImagesIdError = Fetcher.ErrorWrapper<undefined>;

export type GetImagesIdResponse = {
  /**
   * @example 0
   */
  code: number;
  /**
   * @example OK
   */
  message: string;
  data: {
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @example group-uuid-456
     */
    group_id: string;
    /**
     * @example event
     */
    resource_type: "event" | "circle" | "venue";
    /**
     * @example resource-uuid-789
     */
    resource_id: string;
    /**
     * @example poster
     */
    image_type: "poster" | "logo" | "banner" | "gallery";
    /**
     * @example thumb
     */
    variant: "original" | "large" | "medium" | "thumb";
    /**
     * @example /images/events/reitaisai-22/poster_thumb.jpg
     */
    file_path: string;
    /**
     * @example 51200
     */
    file_size?: number | null;
    /**
     * @example 200
     */
    width?: number | null;
    /**
     * @example 300
     */
    height?: number | null;
    /**
     * @example jpeg
     */
    format?: string | null;
    /**
     * @example 2025-01-30T00:00:00Z
     */
    created_at?: string;
    /**
     * @example 2025-01-30T00:00:00Z
     */
    updated_at?: string;
  };
};

export type GetImagesIdVariables = {
  pathParams: GetImagesIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetImagesId = (
  variables: GetImagesIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetImagesIdResponse,
    GetImagesIdError,
    undefined,
    {},
    {},
    GetImagesIdPathParams
  >({ url: "/images/{id}", method: "get", ...variables, signal });

export function getImagesIdQuery(variables: GetImagesIdVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetImagesIdResponse>;
};

export function getImagesIdQuery(
  variables: GetImagesIdVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetImagesIdResponse>)
    | reactQuery.SkipToken;
};

export function getImagesIdQuery(
  variables: GetImagesIdVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/images/{id}",
      operationId: "getImagesId",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetImagesId(variables, signal),
  };
}

export const useSuspenseGetImagesId = <TData = GetImagesIdResponse,>(
  variables: GetImagesIdVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<GetImagesIdResponse, GetImagesIdError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetImagesIdResponse,
    GetImagesIdError,
    TData
  >({
    ...getImagesIdQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetImagesId = <TData = GetImagesIdResponse,>(
  variables: GetImagesIdVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<GetImagesIdResponse, GetImagesIdError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<GetImagesIdResponse, GetImagesIdError, TData>({
    ...getImagesIdQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetImagesCategoryResourceIdPathParams = {
  /**
   * @example event
   */
  category: "event" | "circle" | "venue";
  /**
   * @example resource-uuid-789
   */
  resourceId: string;
};

export type GetImagesCategoryResourceIdQueryParams = {
  /**
   * @example 1
   */
  page?: string;
  /**
   * @example 20
   */
  pageSize?: string;
  /**
   * @example thumb
   */
  variant?: "original" | "large" | "medium" | "thumb";
  /**
   * @example poster
   */
  imageType?: "poster" | "logo" | "banner" | "gallery";
};

export type GetImagesCategoryResourceIdError = Fetcher.ErrorWrapper<undefined>;

export type GetImagesCategoryResourceIdResponse = {
  /**
   * @example 0
   */
  code: number;
  /**
   * @example OK
   */
  message: string;
  data: {
    images: {
      /**
       * @example uuid-123
       */
      id: string;
      /**
       * @example group-uuid-456
       */
      group_id: string;
      /**
       * @example event
       */
      resource_type: "event" | "circle" | "venue";
      /**
       * @example resource-uuid-789
       */
      resource_id: string;
      /**
       * @example poster
       */
      image_type: "poster" | "logo" | "banner" | "gallery";
      /**
       * @example thumb
       */
      variant: "original" | "large" | "medium" | "thumb";
      /**
       * @example /images/events/reitaisai-22/poster_thumb.jpg
       */
      file_path: string;
      /**
       * @example 51200
       */
      file_size?: number | null;
      /**
       * @example 200
       */
      width?: number | null;
      /**
       * @example 300
       */
      height?: number | null;
      /**
       * @example jpeg
       */
      format?: string | null;
      /**
       * @example 2025-01-30T00:00:00Z
       */
      created_at?: string;
      /**
       * @example 2025-01-30T00:00:00Z
       */
      updated_at?: string;
    }[];
    pagination: {
      /**
       * @example 1
       */
      page: number;
      /**
       * @example 20
       */
      pageSize: number;
      /**
       * @example 100
       */
      total: number;
      /**
       * @example 5
       */
      totalPages: number;
    };
  };
};

export type GetImagesCategoryResourceIdVariables = {
  pathParams: GetImagesCategoryResourceIdPathParams;
  queryParams?: GetImagesCategoryResourceIdQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetImagesCategoryResourceId = (
  variables: GetImagesCategoryResourceIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetImagesCategoryResourceIdResponse,
    GetImagesCategoryResourceIdError,
    undefined,
    {},
    GetImagesCategoryResourceIdQueryParams,
    GetImagesCategoryResourceIdPathParams
  >({
    url: "/images/{category}/{resourceId}",
    method: "get",
    ...variables,
    signal,
  });

export function getImagesCategoryResourceIdQuery(
  variables: GetImagesCategoryResourceIdVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (
    options: QueryFnOptions,
  ) => Promise<GetImagesCategoryResourceIdResponse>;
};

export function getImagesCategoryResourceIdQuery(
  variables: GetImagesCategoryResourceIdVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((
        options: QueryFnOptions,
      ) => Promise<GetImagesCategoryResourceIdResponse>)
    | reactQuery.SkipToken;
};

export function getImagesCategoryResourceIdQuery(
  variables: GetImagesCategoryResourceIdVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/images/{category}/{resourceId}",
      operationId: "getImagesCategoryResourceId",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetImagesCategoryResourceId(variables, signal),
  };
}

export const useSuspenseGetImagesCategoryResourceId = <
  TData = GetImagesCategoryResourceIdResponse,
>(
  variables: GetImagesCategoryResourceIdVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetImagesCategoryResourceIdResponse,
      GetImagesCategoryResourceIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetImagesCategoryResourceIdResponse,
    GetImagesCategoryResourceIdError,
    TData
  >({
    ...getImagesCategoryResourceIdQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetImagesCategoryResourceId = <
  TData = GetImagesCategoryResourceIdResponse,
>(
  variables: GetImagesCategoryResourceIdVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetImagesCategoryResourceIdResponse,
      GetImagesCategoryResourceIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetImagesCategoryResourceIdResponse,
    GetImagesCategoryResourceIdError,
    TData
  >({
    ...getImagesCategoryResourceIdQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetRichtextEntityTypeEntityIdContentPathParams = {
  /**
   * @example event
   */
  entityType: "event" | "venue";
  /**
   * @example reitaisai-22
   */
  entityId: string;
};

export type GetRichtextEntityTypeEntityIdContentError =
  Fetcher.ErrorWrapper<undefined>;

export type GetRichtextEntityTypeEntityIdContentResponse = {
  /**
   * @example content-uuid-123
   */
  id: string;
  /**
   * @example event
   */
  entity_type: "event" | "venue";
  /**
   * @example reitaisai-22
   */
  entity_id: string;
  /**
   * @example en
   */
  language_code: "en" | "zh" | "ja";
  /**
   * @example introduction
   */
  content_type: string;
  /**
   * @example {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]}
   */
  content: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  created_at: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  updated_at: string;
};

export type GetRichtextEntityTypeEntityIdContentVariables = {
  pathParams: GetRichtextEntityTypeEntityIdContentPathParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 获取指定实体（事件、场馆、社团）的所有富文本内容
 */
export const fetchGetRichtextEntityTypeEntityIdContent = (
  variables: GetRichtextEntityTypeEntityIdContentVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetRichtextEntityTypeEntityIdContentResponse,
    GetRichtextEntityTypeEntityIdContentError,
    undefined,
    {},
    {},
    GetRichtextEntityTypeEntityIdContentPathParams
  >({
    url: "/rich-text/{entityType}/{entityId}/content",
    method: "get",
    ...variables,
    signal,
  });

/**
 * 获取指定实体（事件、场馆、社团）的所有富文本内容
 */
export function getRichtextEntityTypeEntityIdContentQuery(
  variables: GetRichtextEntityTypeEntityIdContentVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (
    options: QueryFnOptions,
  ) => Promise<GetRichtextEntityTypeEntityIdContentResponse>;
};

export function getRichtextEntityTypeEntityIdContentQuery(
  variables:
    | GetRichtextEntityTypeEntityIdContentVariables
    | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((
        options: QueryFnOptions,
      ) => Promise<GetRichtextEntityTypeEntityIdContentResponse>)
    | reactQuery.SkipToken;
};

export function getRichtextEntityTypeEntityIdContentQuery(
  variables:
    | GetRichtextEntityTypeEntityIdContentVariables
    | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/rich-text/{entityType}/{entityId}/content",
      operationId: "getRichtextEntityTypeEntityIdContent",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetRichtextEntityTypeEntityIdContent(variables, signal),
  };
}

/**
 * 获取指定实体（事件、场馆、社团）的所有富文本内容
 */
export const useSuspenseGetRichtextEntityTypeEntityIdContent = <
  TData = GetRichtextEntityTypeEntityIdContentResponse,
>(
  variables: GetRichtextEntityTypeEntityIdContentVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtextEntityTypeEntityIdContentResponse,
      GetRichtextEntityTypeEntityIdContentError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetRichtextEntityTypeEntityIdContentResponse,
    GetRichtextEntityTypeEntityIdContentError,
    TData
  >({
    ...getRichtextEntityTypeEntityIdContentQuery(
      deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

/**
 * 获取指定实体（事件、场馆、社团）的所有富文本内容
 */
export const useGetRichtextEntityTypeEntityIdContent = <
  TData = GetRichtextEntityTypeEntityIdContentResponse,
>(
  variables:
    | GetRichtextEntityTypeEntityIdContentVariables
    | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtextEntityTypeEntityIdContentResponse,
      GetRichtextEntityTypeEntityIdContentError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetRichtextEntityTypeEntityIdContentResponse,
    GetRichtextEntityTypeEntityIdContentError,
    TData
  >({
    ...getRichtextEntityTypeEntityIdContentQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PostRichtextEntityTypeEntityIdContentPathParams = {
  /**
   * @example event
   */
  entityType: "event" | "venue";
  /**
   * @example reitaisai-22
   */
  entityId: string;
};

export type PostRichtextEntityTypeEntityIdContentError =
  Fetcher.ErrorWrapper<undefined>;

export type PostRichtextEntityTypeEntityIdContentResponse = {
  /**
   * @example content-uuid-123
   */
  id: string;
  /**
   * @example event
   */
  entity_type: "event" | "venue";
  /**
   * @example reitaisai-22
   */
  entity_id: string;
  /**
   * @example en
   */
  language_code: "en" | "zh" | "ja";
  /**
   * @example introduction
   */
  content_type: string;
  /**
   * @example {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]}
   */
  content: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  created_at: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  updated_at: string;
};

export type PostRichtextEntityTypeEntityIdContentRequestBody = {
  entity_type: "event" | "venue";
  /**
   * @minLength 1
   */
  entity_id: string;
  language_code: "en" | "zh" | "ja";
  /**
   * @minLength 1
   */
  content_type: string;
  /**
   * @maxLength 1000000
   */
  content: string;
};

export type PostRichtextEntityTypeEntityIdContentVariables = {
  body: PostRichtextEntityTypeEntityIdContentRequestBody;
  pathParams: PostRichtextEntityTypeEntityIdContentPathParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 创建或更新指定实体的单个富文本内容
 */
export const fetchPostRichtextEntityTypeEntityIdContent = (
  variables: PostRichtextEntityTypeEntityIdContentVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostRichtextEntityTypeEntityIdContentResponse,
    PostRichtextEntityTypeEntityIdContentError,
    PostRichtextEntityTypeEntityIdContentRequestBody,
    {},
    {},
    PostRichtextEntityTypeEntityIdContentPathParams
  >({
    url: "/rich-text/{entityType}/{entityId}/content",
    method: "post",
    ...variables,
    signal,
  });

/**
 * 创建或更新指定实体的单个富文本内容
 */
export const usePostRichtextEntityTypeEntityIdContent = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostRichtextEntityTypeEntityIdContentResponse,
      PostRichtextEntityTypeEntityIdContentError,
      PostRichtextEntityTypeEntityIdContentVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostRichtextEntityTypeEntityIdContentResponse,
    PostRichtextEntityTypeEntityIdContentError,
    PostRichtextEntityTypeEntityIdContentVariables
  >({
    mutationFn: (variables: PostRichtextEntityTypeEntityIdContentVariables) =>
      fetchPostRichtextEntityTypeEntityIdContent(
        deepMerge(fetcherOptions, variables),
      ),
    ...options,
  });
};

export type PutRichtextEntityTypeEntityIdContentPathParams = {
  /**
   * @example event
   */
  entityType: "event" | "venue";
  /**
   * @example reitaisai-22
   */
  entityId: string;
};

export type PutRichtextEntityTypeEntityIdContentError =
  Fetcher.ErrorWrapper<undefined>;

export type PutRichtextEntityTypeEntityIdContentResponse = {
  /**
   * @example content-uuid-123
   */
  id: string;
  /**
   * @example event
   */
  entity_type: "event" | "venue";
  /**
   * @example reitaisai-22
   */
  entity_id: string;
  /**
   * @example en
   */
  language_code: "en" | "zh" | "ja";
  /**
   * @example introduction
   */
  content_type: string;
  /**
   * @example {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]}
   */
  content: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  created_at: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  updated_at: string;
};

export type PutRichtextEntityTypeEntityIdContentRequestBody = {
  /**
   * @minItems 1
   */
  contents: {
    entity_type: "event" | "venue";
    /**
     * @minLength 1
     */
    entity_id: string;
    language_code: "en" | "zh" | "ja";
    /**
     * @minLength 1
     */
    content_type: string;
    /**
     * @maxLength 1000000
     */
    content: string;
  }[];
};

export type PutRichtextEntityTypeEntityIdContentVariables = {
  body: PutRichtextEntityTypeEntityIdContentRequestBody;
  pathParams: PutRichtextEntityTypeEntityIdContentPathParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 批量更新指定实体的所有富文本内容
 */
export const fetchPutRichtextEntityTypeEntityIdContent = (
  variables: PutRichtextEntityTypeEntityIdContentVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PutRichtextEntityTypeEntityIdContentResponse,
    PutRichtextEntityTypeEntityIdContentError,
    PutRichtextEntityTypeEntityIdContentRequestBody,
    {},
    {},
    PutRichtextEntityTypeEntityIdContentPathParams
  >({
    url: "/rich-text/{entityType}/{entityId}/content",
    method: "put",
    ...variables,
    signal,
  });

/**
 * 批量更新指定实体的所有富文本内容
 */
export const usePutRichtextEntityTypeEntityIdContent = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PutRichtextEntityTypeEntityIdContentResponse,
      PutRichtextEntityTypeEntityIdContentError,
      PutRichtextEntityTypeEntityIdContentVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PutRichtextEntityTypeEntityIdContentResponse,
    PutRichtextEntityTypeEntityIdContentError,
    PutRichtextEntityTypeEntityIdContentVariables
  >({
    mutationFn: (variables: PutRichtextEntityTypeEntityIdContentVariables) =>
      fetchPutRichtextEntityTypeEntityIdContent(
        deepMerge(fetcherOptions, variables),
      ),
    ...options,
  });
};

export type DeleteRichtextEntityTypeEntityIdContentPathParams = {
  /**
   * @example event
   */
  entityType: "event" | "venue";
  /**
   * @example reitaisai-22
   */
  entityId: string;
};

export type DeleteRichtextEntityTypeEntityIdContentError =
  Fetcher.ErrorWrapper<undefined>;

export type DeleteRichtextEntityTypeEntityIdContentVariables = {
  pathParams: DeleteRichtextEntityTypeEntityIdContentPathParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 删除指定实体的所有富文本内容
 */
export const fetchDeleteRichtextEntityTypeEntityIdContent = (
  variables: DeleteRichtextEntityTypeEntityIdContentVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    undefined,
    DeleteRichtextEntityTypeEntityIdContentError,
    undefined,
    {},
    {},
    DeleteRichtextEntityTypeEntityIdContentPathParams
  >({
    url: "/rich-text/{entityType}/{entityId}/content",
    method: "delete",
    ...variables,
    signal,
  });

/**
 * 删除指定实体的所有富文本内容
 */
export const useDeleteRichtextEntityTypeEntityIdContent = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      undefined,
      DeleteRichtextEntityTypeEntityIdContentError,
      DeleteRichtextEntityTypeEntityIdContentVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    undefined,
    DeleteRichtextEntityTypeEntityIdContentError,
    DeleteRichtextEntityTypeEntityIdContentVariables
  >({
    mutationFn: (variables: DeleteRichtextEntityTypeEntityIdContentVariables) =>
      fetchDeleteRichtextEntityTypeEntityIdContent(
        deepMerge(fetcherOptions, variables),
      ),
    ...options,
  });
};

export type GetRichtextEntityTypeEntityIdContentContentTypePathParams = {
  /**
   * @example event
   */
  entityType: "event" | "venue";
  /**
   * @example reitaisai-22
   */
  entityId: string;
  /**
   * @minLength 1
   * @example introduction
   */
  contentType: string;
};

export type GetRichtextEntityTypeEntityIdContentContentTypeError =
  Fetcher.ErrorWrapper<undefined>;

export type GetRichtextEntityTypeEntityIdContentContentTypeResponse = {
  content?: string;
};

export type GetRichtextEntityTypeEntityIdContentContentTypeVariables = {
  pathParams: GetRichtextEntityTypeEntityIdContentContentTypePathParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 获取指定实体的特定类型富文本内容
 */
export const fetchGetRichtextEntityTypeEntityIdContentContentType = (
  variables: GetRichtextEntityTypeEntityIdContentContentTypeVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetRichtextEntityTypeEntityIdContentContentTypeResponse,
    GetRichtextEntityTypeEntityIdContentContentTypeError,
    undefined,
    {},
    {},
    GetRichtextEntityTypeEntityIdContentContentTypePathParams
  >({
    url: "/rich-text/{entityType}/{entityId}/content/{contentType}",
    method: "get",
    ...variables,
    signal,
  });

/**
 * 获取指定实体的特定类型富文本内容
 */
export function getRichtextEntityTypeEntityIdContentContentTypeQuery(
  variables: GetRichtextEntityTypeEntityIdContentContentTypeVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (
    options: QueryFnOptions,
  ) => Promise<GetRichtextEntityTypeEntityIdContentContentTypeResponse>;
};

export function getRichtextEntityTypeEntityIdContentContentTypeQuery(
  variables:
    | GetRichtextEntityTypeEntityIdContentContentTypeVariables
    | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((
        options: QueryFnOptions,
      ) => Promise<GetRichtextEntityTypeEntityIdContentContentTypeResponse>)
    | reactQuery.SkipToken;
};

export function getRichtextEntityTypeEntityIdContentContentTypeQuery(
  variables:
    | GetRichtextEntityTypeEntityIdContentContentTypeVariables
    | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/rich-text/{entityType}/{entityId}/content/{contentType}",
      operationId: "getRichtextEntityTypeEntityIdContentContentType",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetRichtextEntityTypeEntityIdContentContentType(
              variables,
              signal,
            ),
  };
}

/**
 * 获取指定实体的特定类型富文本内容
 */
export const useSuspenseGetRichtextEntityTypeEntityIdContentContentType = <
  TData = GetRichtextEntityTypeEntityIdContentContentTypeResponse,
>(
  variables: GetRichtextEntityTypeEntityIdContentContentTypeVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtextEntityTypeEntityIdContentContentTypeResponse,
      GetRichtextEntityTypeEntityIdContentContentTypeError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetRichtextEntityTypeEntityIdContentContentTypeResponse,
    GetRichtextEntityTypeEntityIdContentContentTypeError,
    TData
  >({
    ...getRichtextEntityTypeEntityIdContentContentTypeQuery(
      deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

/**
 * 获取指定实体的特定类型富文本内容
 */
export const useGetRichtextEntityTypeEntityIdContentContentType = <
  TData = GetRichtextEntityTypeEntityIdContentContentTypeResponse,
>(
  variables:
    | GetRichtextEntityTypeEntityIdContentContentTypeVariables
    | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtextEntityTypeEntityIdContentContentTypeResponse,
      GetRichtextEntityTypeEntityIdContentContentTypeError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetRichtextEntityTypeEntityIdContentContentTypeResponse,
    GetRichtextEntityTypeEntityIdContentContentTypeError,
    TData
  >({
    ...getRichtextEntityTypeEntityIdContentContentTypeQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PostRichtextApiUploadImagesError = Fetcher.ErrorWrapper<undefined>;

export type PostRichtextApiUploadImagesResponse = {
  /**
   * 图片访问URL
   *
   * @example /images/content/1640995200000_example.jpg
   */
  url?: string;
};

export type PostRichtextApiUploadImagesRequestBody = {
  /**
   * 图片文件
   *
   * @format binary
   */
  image: Blob;
};

export type PostRichtextApiUploadImagesVariables = {
  body: PostRichtextApiUploadImagesRequestBody;
} & AyafeedContext["fetcherOptions"];

/**
 * 为富文本编辑器提供图片上传功能
 */
export const fetchPostRichtextApiUploadImages = (
  variables: PostRichtextApiUploadImagesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostRichtextApiUploadImagesResponse,
    PostRichtextApiUploadImagesError,
    PostRichtextApiUploadImagesRequestBody,
    {},
    {},
    {}
  >({
    url: "/rich-text/api/upload/images",
    method: "post",
    ...variables,
    signal,
  });

/**
 * 为富文本编辑器提供图片上传功能
 */
export const usePostRichtextApiUploadImages = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostRichtextApiUploadImagesResponse,
      PostRichtextApiUploadImagesError,
      PostRichtextApiUploadImagesVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostRichtextApiUploadImagesResponse,
    PostRichtextApiUploadImagesError,
    PostRichtextApiUploadImagesVariables
  >({
    mutationFn: (variables: PostRichtextApiUploadImagesVariables) =>
      fetchPostRichtextApiUploadImages(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetRichtexttabsConfigsEntityTypeLanguageCodePathParams = {
  /**
   * @example event
   */
  entityType: "event" | "venue";
  /**
   * @example en
   */
  languageCode: "en" | "zh" | "ja";
};

export type GetRichtexttabsConfigsEntityTypeLanguageCodeError =
  Fetcher.ErrorWrapper<undefined>;

export type GetRichtexttabsConfigsEntityTypeLanguageCodeResponse = {
  /**
   * @example config-uuid-123
   */
  id: string;
  /**
   * @example event
   */
  entity_type: "event" | "venue";
  /**
   * @example en
   */
  language_code: "en" | "zh" | "ja";
  /**
   * @minLength 2
   * @maxLength 50
   * @pattern ^[a-zA-Z0-9_-]+$
   * @example introduction
   */
  key: string;
  /**
   * @minLength 1
   * @maxLength 100
   * @example Introduction
   */
  label: string;
  /**
   * @example Enter introduction...
   */
  placeholder?: string;
  /**
   * @example info
   */
  icon?: string;
  /**
   * @minimum 0
   * @example 0
   */
  sort_order: number;
  /**
   * @example true
   */
  is_active: boolean;
  /**
   * @example false
   */
  is_preset: boolean;
  deleted_at?: string;
  deleted_by?: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  created_at: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  updated_at: string;
}[];

export type GetRichtexttabsConfigsEntityTypeLanguageCodeVariables = {
  pathParams: GetRichtexttabsConfigsEntityTypeLanguageCodePathParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 获取指定实体类型和语言的所有活跃标签页配置
 */
export const fetchGetRichtexttabsConfigsEntityTypeLanguageCode = (
  variables: GetRichtexttabsConfigsEntityTypeLanguageCodeVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetRichtexttabsConfigsEntityTypeLanguageCodeResponse,
    GetRichtexttabsConfigsEntityTypeLanguageCodeError,
    undefined,
    {},
    {},
    GetRichtexttabsConfigsEntityTypeLanguageCodePathParams
  >({
    url: "/rich-text-tabs/configs/{entityType}/{languageCode}",
    method: "get",
    ...variables,
    signal,
  });

/**
 * 获取指定实体类型和语言的所有活跃标签页配置
 */
export function getRichtexttabsConfigsEntityTypeLanguageCodeQuery(
  variables: GetRichtexttabsConfigsEntityTypeLanguageCodeVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (
    options: QueryFnOptions,
  ) => Promise<GetRichtexttabsConfigsEntityTypeLanguageCodeResponse>;
};

export function getRichtexttabsConfigsEntityTypeLanguageCodeQuery(
  variables:
    | GetRichtexttabsConfigsEntityTypeLanguageCodeVariables
    | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((
        options: QueryFnOptions,
      ) => Promise<GetRichtexttabsConfigsEntityTypeLanguageCodeResponse>)
    | reactQuery.SkipToken;
};

export function getRichtexttabsConfigsEntityTypeLanguageCodeQuery(
  variables:
    | GetRichtexttabsConfigsEntityTypeLanguageCodeVariables
    | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/rich-text-tabs/configs/{entityType}/{languageCode}",
      operationId: "getRichtexttabsConfigsEntityTypeLanguageCode",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetRichtexttabsConfigsEntityTypeLanguageCode(
              variables,
              signal,
            ),
  };
}

/**
 * 获取指定实体类型和语言的所有活跃标签页配置
 */
export const useSuspenseGetRichtexttabsConfigsEntityTypeLanguageCode = <
  TData = GetRichtexttabsConfigsEntityTypeLanguageCodeResponse,
>(
  variables: GetRichtexttabsConfigsEntityTypeLanguageCodeVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtexttabsConfigsEntityTypeLanguageCodeResponse,
      GetRichtexttabsConfigsEntityTypeLanguageCodeError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetRichtexttabsConfigsEntityTypeLanguageCodeResponse,
    GetRichtexttabsConfigsEntityTypeLanguageCodeError,
    TData
  >({
    ...getRichtexttabsConfigsEntityTypeLanguageCodeQuery(
      deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

/**
 * 获取指定实体类型和语言的所有活跃标签页配置
 */
export const useGetRichtexttabsConfigsEntityTypeLanguageCode = <
  TData = GetRichtexttabsConfigsEntityTypeLanguageCodeResponse,
>(
  variables:
    | GetRichtexttabsConfigsEntityTypeLanguageCodeVariables
    | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtexttabsConfigsEntityTypeLanguageCodeResponse,
      GetRichtexttabsConfigsEntityTypeLanguageCodeError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetRichtexttabsConfigsEntityTypeLanguageCodeResponse,
    GetRichtexttabsConfigsEntityTypeLanguageCodeError,
    TData
  >({
    ...getRichtexttabsConfigsEntityTypeLanguageCodeQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetRichtexttabsConfigsEntityTypeLanguageCodeAllPathParams = {
  /**
   * @example event
   */
  entityType: "event" | "venue";
  /**
   * @example en
   */
  languageCode: "en" | "zh" | "ja";
};

export type GetRichtexttabsConfigsEntityTypeLanguageCodeAllQueryParams = {
  /**
   * @example false
   */
  includeDeleted?: string;
};

export type GetRichtexttabsConfigsEntityTypeLanguageCodeAllError =
  Fetcher.ErrorWrapper<undefined>;

export type GetRichtexttabsConfigsEntityTypeLanguageCodeAllResponse = {
  /**
   * @example config-uuid-123
   */
  id: string;
  /**
   * @example event
   */
  entity_type: "event" | "venue";
  /**
   * @example en
   */
  language_code: "en" | "zh" | "ja";
  /**
   * @minLength 2
   * @maxLength 50
   * @pattern ^[a-zA-Z0-9_-]+$
   * @example introduction
   */
  key: string;
  /**
   * @minLength 1
   * @maxLength 100
   * @example Introduction
   */
  label: string;
  /**
   * @example Enter introduction...
   */
  placeholder?: string;
  /**
   * @example info
   */
  icon?: string;
  /**
   * @minimum 0
   * @example 0
   */
  sort_order: number;
  /**
   * @example true
   */
  is_active: boolean;
  /**
   * @example false
   */
  is_preset: boolean;
  deleted_at?: string;
  deleted_by?: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  created_at: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  updated_at: string;
}[];

export type GetRichtexttabsConfigsEntityTypeLanguageCodeAllVariables = {
  pathParams: GetRichtexttabsConfigsEntityTypeLanguageCodeAllPathParams;
  queryParams?: GetRichtexttabsConfigsEntityTypeLanguageCodeAllQueryParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 获取指定实体类型和语言的所有标签页配置，包括已删除的（管理界面用）
 */
export const fetchGetRichtexttabsConfigsEntityTypeLanguageCodeAll = (
  variables: GetRichtexttabsConfigsEntityTypeLanguageCodeAllVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetRichtexttabsConfigsEntityTypeLanguageCodeAllResponse,
    GetRichtexttabsConfigsEntityTypeLanguageCodeAllError,
    undefined,
    {},
    GetRichtexttabsConfigsEntityTypeLanguageCodeAllQueryParams,
    GetRichtexttabsConfigsEntityTypeLanguageCodeAllPathParams
  >({
    url: "/rich-text-tabs/configs/{entityType}/{languageCode}/all",
    method: "get",
    ...variables,
    signal,
  });

/**
 * 获取指定实体类型和语言的所有标签页配置，包括已删除的（管理界面用）
 */
export function getRichtexttabsConfigsEntityTypeLanguageCodeAllQuery(
  variables: GetRichtexttabsConfigsEntityTypeLanguageCodeAllVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (
    options: QueryFnOptions,
  ) => Promise<GetRichtexttabsConfigsEntityTypeLanguageCodeAllResponse>;
};

export function getRichtexttabsConfigsEntityTypeLanguageCodeAllQuery(
  variables:
    | GetRichtexttabsConfigsEntityTypeLanguageCodeAllVariables
    | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((
        options: QueryFnOptions,
      ) => Promise<GetRichtexttabsConfigsEntityTypeLanguageCodeAllResponse>)
    | reactQuery.SkipToken;
};

export function getRichtexttabsConfigsEntityTypeLanguageCodeAllQuery(
  variables:
    | GetRichtexttabsConfigsEntityTypeLanguageCodeAllVariables
    | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/rich-text-tabs/configs/{entityType}/{languageCode}/all",
      operationId: "getRichtexttabsConfigsEntityTypeLanguageCodeAll",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetRichtexttabsConfigsEntityTypeLanguageCodeAll(
              variables,
              signal,
            ),
  };
}

/**
 * 获取指定实体类型和语言的所有标签页配置，包括已删除的（管理界面用）
 */
export const useSuspenseGetRichtexttabsConfigsEntityTypeLanguageCodeAll = <
  TData = GetRichtexttabsConfigsEntityTypeLanguageCodeAllResponse,
>(
  variables: GetRichtexttabsConfigsEntityTypeLanguageCodeAllVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtexttabsConfigsEntityTypeLanguageCodeAllResponse,
      GetRichtexttabsConfigsEntityTypeLanguageCodeAllError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetRichtexttabsConfigsEntityTypeLanguageCodeAllResponse,
    GetRichtexttabsConfigsEntityTypeLanguageCodeAllError,
    TData
  >({
    ...getRichtexttabsConfigsEntityTypeLanguageCodeAllQuery(
      deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

/**
 * 获取指定实体类型和语言的所有标签页配置，包括已删除的（管理界面用）
 */
export const useGetRichtexttabsConfigsEntityTypeLanguageCodeAll = <
  TData = GetRichtexttabsConfigsEntityTypeLanguageCodeAllResponse,
>(
  variables:
    | GetRichtexttabsConfigsEntityTypeLanguageCodeAllVariables
    | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtexttabsConfigsEntityTypeLanguageCodeAllResponse,
      GetRichtexttabsConfigsEntityTypeLanguageCodeAllError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetRichtexttabsConfigsEntityTypeLanguageCodeAllResponse,
    GetRichtexttabsConfigsEntityTypeLanguageCodeAllError,
    TData
  >({
    ...getRichtexttabsConfigsEntityTypeLanguageCodeAllQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PostRichtexttabsConfigsError = Fetcher.ErrorWrapper<undefined>;

export type PostRichtexttabsConfigsResponse = {
  /**
   * @example config-uuid-123
   */
  id: string;
  /**
   * @example event
   */
  entity_type: "event" | "venue";
  /**
   * @example en
   */
  language_code: "en" | "zh" | "ja";
  /**
   * @minLength 2
   * @maxLength 50
   * @pattern ^[a-zA-Z0-9_-]+$
   * @example introduction
   */
  key: string;
  /**
   * @minLength 1
   * @maxLength 100
   * @example Introduction
   */
  label: string;
  /**
   * @example Enter introduction...
   */
  placeholder?: string;
  /**
   * @example info
   */
  icon?: string;
  /**
   * @minimum 0
   * @example 0
   */
  sort_order: number;
  /**
   * @example true
   */
  is_active: boolean;
  /**
   * @example false
   */
  is_preset: boolean;
  deleted_at?: string;
  deleted_by?: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  created_at: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  updated_at: string;
};

export type PostRichtexttabsConfigsRequestBody = {
  entity_type: "event" | "venue";
  language_code: "en" | "zh" | "ja";
  /**
   * @minLength 1
   * @maxLength 100
   */
  label: string;
  /**
   * @maxLength 200
   */
  placeholder?: string;
  /**
   * @maxLength 50
   */
  icon?: string;
  /**
   * @minimum 0
   */
  sort_order?: number;
  is_active?: boolean;
  is_preset?: boolean;
};

export type PostRichtexttabsConfigsVariables = {
  body: PostRichtexttabsConfigsRequestBody;
} & AyafeedContext["fetcherOptions"];

/**
 * 为指定实体类型和语言创建新的标签页配置。Key 由后端自动生成
 */
export const fetchPostRichtexttabsConfigs = (
  variables: PostRichtexttabsConfigsVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostRichtexttabsConfigsResponse,
    PostRichtexttabsConfigsError,
    PostRichtexttabsConfigsRequestBody,
    {},
    {},
    {}
  >({ url: "/rich-text-tabs/configs", method: "post", ...variables, signal });

/**
 * 为指定实体类型和语言创建新的标签页配置。Key 由后端自动生成
 */
export const usePostRichtexttabsConfigs = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostRichtexttabsConfigsResponse,
      PostRichtexttabsConfigsError,
      PostRichtexttabsConfigsVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostRichtexttabsConfigsResponse,
    PostRichtexttabsConfigsError,
    PostRichtexttabsConfigsVariables
  >({
    mutationFn: (variables: PostRichtexttabsConfigsVariables) =>
      fetchPostRichtexttabsConfigs(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type PutRichtexttabsConfigsIdPathParams = {
  /**
   * @example config-uuid-123
   */
  id: string;
};

export type PutRichtexttabsConfigsIdError = Fetcher.ErrorWrapper<undefined>;

export type PutRichtexttabsConfigsIdResponse = {
  /**
   * @example config-uuid-123
   */
  id: string;
  /**
   * @example event
   */
  entity_type: "event" | "venue";
  /**
   * @example en
   */
  language_code: "en" | "zh" | "ja";
  /**
   * @minLength 2
   * @maxLength 50
   * @pattern ^[a-zA-Z0-9_-]+$
   * @example introduction
   */
  key: string;
  /**
   * @minLength 1
   * @maxLength 100
   * @example Introduction
   */
  label: string;
  /**
   * @example Enter introduction...
   */
  placeholder?: string;
  /**
   * @example info
   */
  icon?: string;
  /**
   * @minimum 0
   * @example 0
   */
  sort_order: number;
  /**
   * @example true
   */
  is_active: boolean;
  /**
   * @example false
   */
  is_preset: boolean;
  deleted_at?: string;
  deleted_by?: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  created_at: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  updated_at: string;
};

export type PutRichtexttabsConfigsIdRequestBody = {
  /**
   * @minLength 1
   * @maxLength 100
   */
  label?: string;
  /**
   * @maxLength 200
   */
  placeholder?: string;
  /**
   * @maxLength 50
   */
  icon?: string;
  /**
   * @minimum 0
   */
  sort_order?: number;
  is_active?: boolean;
};

export type PutRichtexttabsConfigsIdVariables = {
  body?: PutRichtexttabsConfigsIdRequestBody;
  pathParams: PutRichtexttabsConfigsIdPathParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 更新指定的标签页配置
 */
export const fetchPutRichtexttabsConfigsId = (
  variables: PutRichtexttabsConfigsIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PutRichtexttabsConfigsIdResponse,
    PutRichtexttabsConfigsIdError,
    PutRichtexttabsConfigsIdRequestBody,
    {},
    {},
    PutRichtexttabsConfigsIdPathParams
  >({
    url: "/rich-text-tabs/configs/{id}",
    method: "put",
    ...variables,
    signal,
  });

/**
 * 更新指定的标签页配置
 */
export const usePutRichtexttabsConfigsId = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PutRichtexttabsConfigsIdResponse,
      PutRichtexttabsConfigsIdError,
      PutRichtexttabsConfigsIdVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PutRichtexttabsConfigsIdResponse,
    PutRichtexttabsConfigsIdError,
    PutRichtexttabsConfigsIdVariables
  >({
    mutationFn: (variables: PutRichtexttabsConfigsIdVariables) =>
      fetchPutRichtexttabsConfigsId(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type DeleteRichtexttabsConfigsIdPathParams = {
  /**
   * @example config-uuid-123
   */
  id: string;
};

export type DeleteRichtexttabsConfigsIdError = Fetcher.ErrorWrapper<undefined>;

export type DeleteRichtexttabsConfigsIdResponse = {
  success: boolean;
  message: string;
  affected_count?: number;
  data?: void | null;
};

export type DeleteRichtexttabsConfigsIdRequestBody = {
  /**
   * @minLength 1
   */
  deleted_by: string;
};

export type DeleteRichtexttabsConfigsIdVariables = {
  body: DeleteRichtexttabsConfigsIdRequestBody;
  pathParams: DeleteRichtexttabsConfigsIdPathParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 软删除指定的标签页配置（预设配置不能删除）
 */
export const fetchDeleteRichtexttabsConfigsId = (
  variables: DeleteRichtexttabsConfigsIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    DeleteRichtexttabsConfigsIdResponse,
    DeleteRichtexttabsConfigsIdError,
    DeleteRichtexttabsConfigsIdRequestBody,
    {},
    {},
    DeleteRichtexttabsConfigsIdPathParams
  >({
    url: "/rich-text-tabs/configs/{id}",
    method: "delete",
    ...variables,
    signal,
  });

/**
 * 软删除指定的标签页配置（预设配置不能删除）
 */
export const useDeleteRichtexttabsConfigsId = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      DeleteRichtexttabsConfigsIdResponse,
      DeleteRichtexttabsConfigsIdError,
      DeleteRichtexttabsConfigsIdVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    DeleteRichtexttabsConfigsIdResponse,
    DeleteRichtexttabsConfigsIdError,
    DeleteRichtexttabsConfigsIdVariables
  >({
    mutationFn: (variables: DeleteRichtexttabsConfigsIdVariables) =>
      fetchDeleteRichtexttabsConfigsId(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type PostRichtexttabsConfigsIdRestorePathParams = {
  /**
   * @example config-uuid-123
   */
  id: string;
};

export type PostRichtexttabsConfigsIdRestoreError =
  Fetcher.ErrorWrapper<undefined>;

export type PostRichtexttabsConfigsIdRestoreResponse = {
  success: boolean;
  message: string;
  affected_count?: number;
  data?: void | null;
};

export type PostRichtexttabsConfigsIdRestoreVariables = {
  pathParams: PostRichtexttabsConfigsIdRestorePathParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 恢复指定的已删除标签页配置
 */
export const fetchPostRichtexttabsConfigsIdRestore = (
  variables: PostRichtexttabsConfigsIdRestoreVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostRichtexttabsConfigsIdRestoreResponse,
    PostRichtexttabsConfigsIdRestoreError,
    undefined,
    {},
    {},
    PostRichtexttabsConfigsIdRestorePathParams
  >({
    url: "/rich-text-tabs/configs/{id}/restore",
    method: "post",
    ...variables,
    signal,
  });

/**
 * 恢复指定的已删除标签页配置
 */
export const usePostRichtexttabsConfigsIdRestore = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostRichtexttabsConfigsIdRestoreResponse,
      PostRichtexttabsConfigsIdRestoreError,
      PostRichtexttabsConfigsIdRestoreVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostRichtexttabsConfigsIdRestoreResponse,
    PostRichtexttabsConfigsIdRestoreError,
    PostRichtexttabsConfigsIdRestoreVariables
  >({
    mutationFn: (variables: PostRichtexttabsConfigsIdRestoreVariables) =>
      fetchPostRichtexttabsConfigsIdRestore(
        deepMerge(fetcherOptions, variables),
      ),
    ...options,
  });
};

export type PostRichtexttabsConfigsReorderError =
  Fetcher.ErrorWrapper<undefined>;

export type PostRichtexttabsConfigsReorderResponse = {
  success: boolean;
  message: string;
  affected_count?: number;
  data?: void | null;
};

export type PostRichtexttabsConfigsReorderRequestBody = {
  entity_type: "event" | "venue";
  language_code: "en" | "zh" | "ja";
  orders: {
    id: string;
    /**
     * @minimum 0
     */
    sort_order: number;
  }[];
};

export type PostRichtexttabsConfigsReorderVariables = {
  body: PostRichtexttabsConfigsReorderRequestBody;
} & AyafeedContext["fetcherOptions"];

/**
 * 批量更新标签页配置的排序
 */
export const fetchPostRichtexttabsConfigsReorder = (
  variables: PostRichtexttabsConfigsReorderVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostRichtexttabsConfigsReorderResponse,
    PostRichtexttabsConfigsReorderError,
    PostRichtexttabsConfigsReorderRequestBody,
    {},
    {},
    {}
  >({
    url: "/rich-text-tabs/configs/reorder",
    method: "post",
    ...variables,
    signal,
  });

/**
 * 批量更新标签页配置的排序
 */
export const usePostRichtexttabsConfigsReorder = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostRichtexttabsConfigsReorderResponse,
      PostRichtexttabsConfigsReorderError,
      PostRichtexttabsConfigsReorderVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostRichtexttabsConfigsReorderResponse,
    PostRichtexttabsConfigsReorderError,
    PostRichtexttabsConfigsReorderVariables
  >({
    mutationFn: (variables: PostRichtexttabsConfigsReorderVariables) =>
      fetchPostRichtexttabsConfigsReorder(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type PostRichtexttabsConfigsBatchstatusError =
  Fetcher.ErrorWrapper<undefined>;

export type PostRichtexttabsConfigsBatchstatusResponse = {
  success: boolean;
  message: string;
  affected_count?: number;
  data?: void | null;
};

export type PostRichtexttabsConfigsBatchstatusRequestBody = {
  /**
   * @minItems 1
   */
  ids: string[];
  is_active: boolean;
};

export type PostRichtexttabsConfigsBatchstatusVariables = {
  body: PostRichtexttabsConfigsBatchstatusRequestBody;
} & AyafeedContext["fetcherOptions"];

/**
 * 批量启用或禁用标签页配置
 */
export const fetchPostRichtexttabsConfigsBatchstatus = (
  variables: PostRichtexttabsConfigsBatchstatusVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostRichtexttabsConfigsBatchstatusResponse,
    PostRichtexttabsConfigsBatchstatusError,
    PostRichtexttabsConfigsBatchstatusRequestBody,
    {},
    {},
    {}
  >({
    url: "/rich-text-tabs/configs/batch-status",
    method: "post",
    ...variables,
    signal,
  });

/**
 * 批量启用或禁用标签页配置
 */
export const usePostRichtexttabsConfigsBatchstatus = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostRichtexttabsConfigsBatchstatusResponse,
      PostRichtexttabsConfigsBatchstatusError,
      PostRichtexttabsConfigsBatchstatusVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostRichtexttabsConfigsBatchstatusResponse,
    PostRichtexttabsConfigsBatchstatusError,
    PostRichtexttabsConfigsBatchstatusVariables
  >({
    mutationFn: (variables: PostRichtexttabsConfigsBatchstatusVariables) =>
      fetchPostRichtexttabsConfigsBatchstatus(
        deepMerge(fetcherOptions, variables),
      ),
    ...options,
  });
};

export type GetRichtexttabsConfigsSuggestkeysQueryParams = {
  /**
   * @minLength 1
   * @example 活动介绍
   */
  label: string;
  /**
   * @example event
   */
  entity_type: "event" | "venue";
};

export type GetRichtexttabsConfigsSuggestkeysError =
  Fetcher.ErrorWrapper<undefined>;

export type GetRichtexttabsConfigsSuggestkeysResponse = {
  code: number;
  message: string;
  data: {
    suggestions: string[];
    recommended: string;
  };
};

export type GetRichtexttabsConfigsSuggestkeysVariables = {
  queryParams: GetRichtexttabsConfigsSuggestkeysQueryParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 根据标签名称和实体类型获取推荐的 key 值
 */
export const fetchGetRichtexttabsConfigsSuggestkeys = (
  variables: GetRichtexttabsConfigsSuggestkeysVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetRichtexttabsConfigsSuggestkeysResponse,
    GetRichtexttabsConfigsSuggestkeysError,
    undefined,
    {},
    GetRichtexttabsConfigsSuggestkeysQueryParams,
    {}
  >({
    url: "/rich-text-tabs/configs/suggest-keys",
    method: "get",
    ...variables,
    signal,
  });

/**
 * 根据标签名称和实体类型获取推荐的 key 值
 */
export function getRichtexttabsConfigsSuggestkeysQuery(
  variables: GetRichtexttabsConfigsSuggestkeysVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (
    options: QueryFnOptions,
  ) => Promise<GetRichtexttabsConfigsSuggestkeysResponse>;
};

export function getRichtexttabsConfigsSuggestkeysQuery(
  variables: GetRichtexttabsConfigsSuggestkeysVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((
        options: QueryFnOptions,
      ) => Promise<GetRichtexttabsConfigsSuggestkeysResponse>)
    | reactQuery.SkipToken;
};

export function getRichtexttabsConfigsSuggestkeysQuery(
  variables: GetRichtexttabsConfigsSuggestkeysVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/rich-text-tabs/configs/suggest-keys",
      operationId: "getRichtexttabsConfigsSuggestkeys",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetRichtexttabsConfigsSuggestkeys(variables, signal),
  };
}

/**
 * 根据标签名称和实体类型获取推荐的 key 值
 */
export const useSuspenseGetRichtexttabsConfigsSuggestkeys = <
  TData = GetRichtexttabsConfigsSuggestkeysResponse,
>(
  variables: GetRichtexttabsConfigsSuggestkeysVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtexttabsConfigsSuggestkeysResponse,
      GetRichtexttabsConfigsSuggestkeysError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetRichtexttabsConfigsSuggestkeysResponse,
    GetRichtexttabsConfigsSuggestkeysError,
    TData
  >({
    ...getRichtexttabsConfigsSuggestkeysQuery(
      deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

/**
 * 根据标签名称和实体类型获取推荐的 key 值
 */
export const useGetRichtexttabsConfigsSuggestkeys = <
  TData = GetRichtexttabsConfigsSuggestkeysResponse,
>(
  variables: GetRichtexttabsConfigsSuggestkeysVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtexttabsConfigsSuggestkeysResponse,
      GetRichtexttabsConfigsSuggestkeysError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetRichtexttabsConfigsSuggestkeysResponse,
    GetRichtexttabsConfigsSuggestkeysError,
    TData
  >({
    ...getRichtexttabsConfigsSuggestkeysQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetRichtexttabsTabsEntityTypeEntityIdLanguageCodePathParams = {
  /**
   * @example event
   */
  entityType: "event" | "venue";
  /**
   * @example reitaisai-22
   */
  entityId: string;
  /**
   * @example en
   */
  languageCode: "en" | "zh" | "ja";
};

export type GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeQueryParams = {
  /**
   * @example false
   */
  includeInactive?: string;
};

export type GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeError =
  Fetcher.ErrorWrapper<undefined>;

export type GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeResponse = {
  entity_type: "event" | "venue";
  entity_id: string;
  language_code: "en" | "zh" | "ja";
  tabs: {
    config: {
      /**
       * @example config-uuid-123
       */
      id: string;
      /**
       * @example event
       */
      entity_type: "event" | "venue";
      /**
       * @example en
       */
      language_code: "en" | "zh" | "ja";
      /**
       * @minLength 2
       * @maxLength 50
       * @pattern ^[a-zA-Z0-9_-]+$
       * @example introduction
       */
      key: string;
      /**
       * @minLength 1
       * @maxLength 100
       * @example Introduction
       */
      label: string;
      /**
       * @example Enter introduction...
       */
      placeholder?: string;
      /**
       * @example info
       */
      icon?: string;
      /**
       * @minimum 0
       * @example 0
       */
      sort_order: number;
      /**
       * @example true
       */
      is_active: boolean;
      /**
       * @example false
       */
      is_preset: boolean;
      deleted_at?: string;
      deleted_by?: string;
      /**
       * @example 2025-01-04T10:00:00.000Z
       */
      created_at: string;
      /**
       * @example 2025-01-04T10:00:00.000Z
       */
      updated_at: string;
    };
    content?: {
      /**
       * @example content-uuid-123
       */
      id: string;
      /**
       * @example event
       */
      entity_type: "event" | "venue";
      /**
       * @example reitaisai-22
       */
      entity_id: string;
      /**
       * @example en
       */
      language_code: "en" | "zh" | "ja";
      /**
       * @example introduction
       */
      content_type: string;
      /**
       * @example {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]}
       */
      content: string;
      /**
       * @example 2025-01-04T10:00:00.000Z
       */
      created_at: string;
      /**
       * @example 2025-01-04T10:00:00.000Z
       */
      updated_at: string;
    };
  }[];
};

export type GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeVariables = {
  pathParams: GetRichtexttabsTabsEntityTypeEntityIdLanguageCodePathParams;
  queryParams?: GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeQueryParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 获取指定实体的标签页配置和内容数据
 */
export const fetchGetRichtexttabsTabsEntityTypeEntityIdLanguageCode = (
  variables: GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeResponse,
    GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeError,
    undefined,
    {},
    GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeQueryParams,
    GetRichtexttabsTabsEntityTypeEntityIdLanguageCodePathParams
  >({
    url: "/rich-text-tabs/tabs/{entityType}/{entityId}/{languageCode}",
    method: "get",
    ...variables,
    signal,
  });

/**
 * 获取指定实体的标签页配置和内容数据
 */
export function getRichtexttabsTabsEntityTypeEntityIdLanguageCodeQuery(
  variables: GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (
    options: QueryFnOptions,
  ) => Promise<GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeResponse>;
};

export function getRichtexttabsTabsEntityTypeEntityIdLanguageCodeQuery(
  variables:
    | GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeVariables
    | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((
        options: QueryFnOptions,
      ) => Promise<GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeResponse>)
    | reactQuery.SkipToken;
};

export function getRichtexttabsTabsEntityTypeEntityIdLanguageCodeQuery(
  variables:
    | GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeVariables
    | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/rich-text-tabs/tabs/{entityType}/{entityId}/{languageCode}",
      operationId: "getRichtexttabsTabsEntityTypeEntityIdLanguageCode",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetRichtexttabsTabsEntityTypeEntityIdLanguageCode(
              variables,
              signal,
            ),
  };
}

/**
 * 获取指定实体的标签页配置和内容数据
 */
export const useSuspenseGetRichtexttabsTabsEntityTypeEntityIdLanguageCode = <
  TData = GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeResponse,
>(
  variables: GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeResponse,
      GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeResponse,
    GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeError,
    TData
  >({
    ...getRichtexttabsTabsEntityTypeEntityIdLanguageCodeQuery(
      deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

/**
 * 获取指定实体的标签页配置和内容数据
 */
export const useGetRichtexttabsTabsEntityTypeEntityIdLanguageCode = <
  TData = GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeResponse,
>(
  variables:
    | GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeVariables
    | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeResponse,
      GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeResponse,
    GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeError,
    TData
  >({
    ...getRichtexttabsTabsEntityTypeEntityIdLanguageCodeQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PostRichtexttabsContentError = Fetcher.ErrorWrapper<undefined>;

export type PostRichtexttabsContentResponse = {
  /**
   * @example content-uuid-123
   */
  id: string;
  /**
   * @example event
   */
  entity_type: "event" | "venue";
  /**
   * @example reitaisai-22
   */
  entity_id: string;
  /**
   * @example en
   */
  language_code: "en" | "zh" | "ja";
  /**
   * @example introduction
   */
  content_type: string;
  /**
   * @example {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]}
   */
  content: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  created_at: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  updated_at: string;
};

export type PostRichtexttabsContentRequestBody = {
  entity_type: "event" | "venue";
  /**
   * @minLength 1
   */
  entity_id: string;
  language_code: "en" | "zh" | "ja";
  /**
   * @minLength 1
   */
  content_type: string;
  /**
   * @maxLength 1000000
   */
  content: string;
};

export type PostRichtexttabsContentVariables = {
  body: PostRichtexttabsContentRequestBody;
} & AyafeedContext["fetcherOptions"];

/**
 * 为指定实体、语言和内容类型创建或更新富文本内容
 */
export const fetchPostRichtexttabsContent = (
  variables: PostRichtexttabsContentVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostRichtexttabsContentResponse,
    PostRichtexttabsContentError,
    PostRichtexttabsContentRequestBody,
    {},
    {},
    {}
  >({ url: "/rich-text-tabs/content", method: "post", ...variables, signal });

/**
 * 为指定实体、语言和内容类型创建或更新富文本内容
 */
export const usePostRichtexttabsContent = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostRichtexttabsContentResponse,
      PostRichtexttabsContentError,
      PostRichtexttabsContentVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostRichtexttabsContentResponse,
    PostRichtexttabsContentError,
    PostRichtexttabsContentVariables
  >({
    mutationFn: (variables: PostRichtexttabsContentVariables) =>
      fetchPostRichtexttabsContent(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type PostRichtexttabsContentBatchError = Fetcher.ErrorWrapper<undefined>;

export type PostRichtexttabsContentBatchResponse = {
  /**
   * @example content-uuid-123
   */
  id: string;
  /**
   * @example event
   */
  entity_type: "event" | "venue";
  /**
   * @example reitaisai-22
   */
  entity_id: string;
  /**
   * @example en
   */
  language_code: "en" | "zh" | "ja";
  /**
   * @example introduction
   */
  content_type: string;
  /**
   * @example {"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Rich text content"}]}]}
   */
  content: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  created_at: string;
  /**
   * @example 2025-01-04T10:00:00.000Z
   */
  updated_at: string;
}[];

export type PostRichtexttabsContentBatchRequestBody = {
  entity_type: "event" | "venue";
  entity_id: string;
  language_code: "en" | "zh" | "ja";
  contents: {
    [key: string]: string;
  };
};

export type PostRichtexttabsContentBatchVariables = {
  body: PostRichtexttabsContentBatchRequestBody;
} & AyafeedContext["fetcherOptions"];

/**
 * 批量为指定实体和语言创建或更新多个内容类型的富文本内容
 */
export const fetchPostRichtexttabsContentBatch = (
  variables: PostRichtexttabsContentBatchVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostRichtexttabsContentBatchResponse,
    PostRichtexttabsContentBatchError,
    PostRichtexttabsContentBatchRequestBody,
    {},
    {},
    {}
  >({
    url: "/rich-text-tabs/content/batch",
    method: "post",
    ...variables,
    signal,
  });

/**
 * 批量为指定实体和语言创建或更新多个内容类型的富文本内容
 */
export const usePostRichtexttabsContentBatch = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostRichtexttabsContentBatchResponse,
      PostRichtexttabsContentBatchError,
      PostRichtexttabsContentBatchVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostRichtexttabsContentBatchResponse,
    PostRichtexttabsContentBatchError,
    PostRichtexttabsContentBatchVariables
  >({
    mutationFn: (variables: PostRichtexttabsContentBatchVariables) =>
      fetchPostRichtexttabsContentBatch(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetRichtexttabsHealthPresetconfigsError =
  Fetcher.ErrorWrapper<undefined>;

export type GetRichtexttabsHealthPresetconfigsResponse = {
  code: number;
  message: string;
  data: {
    isComplete: boolean;
    missing: string[];
    existing: number;
    checked: number;
  };
};

export type GetRichtexttabsHealthPresetconfigsVariables =
  AyafeedContext["fetcherOptions"];

/**
 * 检查 Rich Text Tabs 预设配置是否完整
 */
export const fetchGetRichtexttabsHealthPresetconfigs = (
  variables: GetRichtexttabsHealthPresetconfigsVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetRichtexttabsHealthPresetconfigsResponse,
    GetRichtexttabsHealthPresetconfigsError,
    undefined,
    {},
    {},
    {}
  >({
    url: "/rich-text-tabs/health/preset-configs",
    method: "get",
    ...variables,
    signal,
  });

/**
 * 检查 Rich Text Tabs 预设配置是否完整
 */
export function getRichtexttabsHealthPresetconfigsQuery(
  variables: GetRichtexttabsHealthPresetconfigsVariables,
): {
  queryKey: reactQuery.QueryKey;
  queryFn: (
    options: QueryFnOptions,
  ) => Promise<GetRichtexttabsHealthPresetconfigsResponse>;
};

export function getRichtexttabsHealthPresetconfigsQuery(
  variables: GetRichtexttabsHealthPresetconfigsVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((
        options: QueryFnOptions,
      ) => Promise<GetRichtexttabsHealthPresetconfigsResponse>)
    | reactQuery.SkipToken;
};

export function getRichtexttabsHealthPresetconfigsQuery(
  variables: GetRichtexttabsHealthPresetconfigsVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/rich-text-tabs/health/preset-configs",
      operationId: "getRichtexttabsHealthPresetconfigs",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetRichtexttabsHealthPresetconfigs(variables, signal),
  };
}

/**
 * 检查 Rich Text Tabs 预设配置是否完整
 */
export const useSuspenseGetRichtexttabsHealthPresetconfigs = <
  TData = GetRichtexttabsHealthPresetconfigsResponse,
>(
  variables: GetRichtexttabsHealthPresetconfigsVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtexttabsHealthPresetconfigsResponse,
      GetRichtexttabsHealthPresetconfigsError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetRichtexttabsHealthPresetconfigsResponse,
    GetRichtexttabsHealthPresetconfigsError,
    TData
  >({
    ...getRichtexttabsHealthPresetconfigsQuery(
      deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

/**
 * 检查 Rich Text Tabs 预设配置是否完整
 */
export const useGetRichtexttabsHealthPresetconfigs = <
  TData = GetRichtexttabsHealthPresetconfigsResponse,
>(
  variables: GetRichtexttabsHealthPresetconfigsVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetRichtexttabsHealthPresetconfigsResponse,
      GetRichtexttabsHealthPresetconfigsError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetRichtexttabsHealthPresetconfigsResponse,
    GetRichtexttabsHealthPresetconfigsError,
    TData
  >({
    ...getRichtexttabsHealthPresetconfigsQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PostRichtexttabsRepairEntityTypeEntityIdPathParams = {
  entityType: "event" | "venue";
  /**
   * @minLength 1
   */
  entityId: string;
};

export type PostRichtexttabsRepairEntityTypeEntityIdError =
  Fetcher.ErrorWrapper<undefined>;

export type PostRichtexttabsRepairEntityTypeEntityIdResponse = {
  code: number;
  message: string;
  data: {
    entity_type: string;
    entity_id: string;
    before: {
      isComplete: boolean;
      expectedCount: number;
      actualCount: number;
      missingCount: number;
    };
    after: {
      repaired: number;
      errors: string[];
    };
  };
};

export type PostRichtexttabsRepairEntityTypeEntityIdVariables = {
  pathParams: PostRichtexttabsRepairEntityTypeEntityIdPathParams;
} & AyafeedContext["fetcherOptions"];

/**
 * 检查并修复特定实体缺失的富文本内容记录
 */
export const fetchPostRichtexttabsRepairEntityTypeEntityId = (
  variables: PostRichtexttabsRepairEntityTypeEntityIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostRichtexttabsRepairEntityTypeEntityIdResponse,
    PostRichtexttabsRepairEntityTypeEntityIdError,
    undefined,
    {},
    {},
    PostRichtexttabsRepairEntityTypeEntityIdPathParams
  >({
    url: "/rich-text-tabs/repair/{entityType}/{entityId}",
    method: "post",
    ...variables,
    signal,
  });

/**
 * 检查并修复特定实体缺失的富文本内容记录
 */
export const usePostRichtexttabsRepairEntityTypeEntityId = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostRichtexttabsRepairEntityTypeEntityIdResponse,
      PostRichtexttabsRepairEntityTypeEntityIdError,
      PostRichtexttabsRepairEntityTypeEntityIdVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostRichtexttabsRepairEntityTypeEntityIdResponse,
    PostRichtexttabsRepairEntityTypeEntityIdError,
    PostRichtexttabsRepairEntityTypeEntityIdVariables
  >({
    mutationFn: (
      variables: PostRichtexttabsRepairEntityTypeEntityIdVariables,
    ) =>
      fetchPostRichtexttabsRepairEntityTypeEntityId(
        deepMerge(fetcherOptions, variables),
      ),
    ...options,
  });
};

export type GetAdminEventsQueryParams = {
  /**
   * @example 1
   */
  page?: string;
  /**
   * @example 50
   */
  pageSize?: string;
  /**
   * @example 例大祭
   */
  keyword?: string;
  /**
   * @example 20250101
   */
  date_from?: string;
  /**
   * @example 20251231
   */
  date_to?: string;
};

export type GetAdminEventsError = Fetcher.ErrorWrapper<{
  status: 400;
  payload: Schemas.ErrorResponse;
}>;

export type GetAdminEventsVariables = {
  queryParams?: GetAdminEventsQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetAdminEvents = (
  variables: GetAdminEventsVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.PaginatedResult,
    GetAdminEventsError,
    undefined,
    {},
    GetAdminEventsQueryParams,
    {}
  >({ url: "/admin/events", method: "get", ...variables, signal });

export function getAdminEventsQuery(variables: GetAdminEventsVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<Schemas.PaginatedResult>;
};

export function getAdminEventsQuery(
  variables: GetAdminEventsVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<Schemas.PaginatedResult>)
    | reactQuery.SkipToken;
};

export function getAdminEventsQuery(
  variables: GetAdminEventsVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/admin/events",
      operationId: "getAdminEvents",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetAdminEvents(variables, signal),
  };
}

export const useSuspenseGetAdminEvents = <TData = Schemas.PaginatedResult,>(
  variables: GetAdminEventsVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      Schemas.PaginatedResult,
      GetAdminEventsError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    Schemas.PaginatedResult,
    GetAdminEventsError,
    TData
  >({
    ...getAdminEventsQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAdminEvents = <TData = Schemas.PaginatedResult,>(
  variables: GetAdminEventsVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      Schemas.PaginatedResult,
      GetAdminEventsError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    Schemas.PaginatedResult,
    GetAdminEventsError,
    TData
  >({
    ...getAdminEventsQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PostAdminEventsError = Fetcher.ErrorWrapper<{
  status: 400;
  payload: Schemas.ErrorResponse;
}>;

export type PostAdminEventsRequestBody = {
  /**
   * @example Reitaisai 22
   */
  name_en: string;
  /**
   * @example 第二十二回博麗神社例大祭
   */
  name_ja: string;
  /**
   * @example 第二十二回博丽神社例大祭
   */
  name_zh: string;
  /**
   * @example May 3, 2025 (Sat) 10:30 – 15:30
   */
  date_en: string;
  /**
   * @example 2025年5月3日(土・祝) 10:30 – 15:30
   */
  date_ja: string;
  /**
   * @example 2025年5月3日(周六) 10:30 – 15:30
   */
  date_zh: string;
  /**
   * @example 20250503
   */
  date_sort?: number;
  image_url?: string | null;
  /**
   * @example tokyo-big-sight
   */
  venue_id: string;
  url?: string | null;
};

export type PostAdminEventsVariables = {
  body: PostAdminEventsRequestBody;
} & AyafeedContext["fetcherOptions"];

export const fetchPostAdminEvents = (
  variables: PostAdminEventsVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.SuccessResponse,
    PostAdminEventsError,
    PostAdminEventsRequestBody,
    {},
    {},
    {}
  >({ url: "/admin/events", method: "post", ...variables, signal });

export const usePostAdminEvents = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      Schemas.SuccessResponse,
      PostAdminEventsError,
      PostAdminEventsVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    Schemas.SuccessResponse,
    PostAdminEventsError,
    PostAdminEventsVariables
  >({
    mutationFn: (variables: PostAdminEventsVariables) =>
      fetchPostAdminEvents(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetAdminEventsIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type GetAdminEventsIdError = Fetcher.ErrorWrapper<{
  status: 404;
  payload: Schemas.ErrorResponse;
}>;

export type GetAdminEventsIdResponse = {
  /**
   * @example uuid-123
   */
  id: string;
  /**
   * @example Reitaisai 22
   */
  name_en: string;
  /**
   * @example 第二十二回博麗神社例大祭
   */
  name_ja: string;
  /**
   * @example 第二十二回博丽神社例大祭
   */
  name_zh: string;
  /**
   * @example May 3, 2025 (Sat) 10:30 – 15:30
   */
  date_en: string;
  /**
   * @example 2025年5月3日(土・祝) 10:30 – 15:30
   */
  date_ja: string;
  /**
   * @example 2025年5月3日(周六) 10:30 – 15:30
   */
  date_zh: string;
  /**
   * @example 20250503
   */
  date_sort?: number;
  image_url?: string | null;
  /**
   * @example tokyo-big-sight
   */
  venue_id: string;
  url?: string | null;
  created_at?: string;
  updated_at?: string;
};

export type GetAdminEventsIdVariables = {
  pathParams: GetAdminEventsIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetAdminEventsId = (
  variables: GetAdminEventsIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetAdminEventsIdResponse,
    GetAdminEventsIdError,
    undefined,
    {},
    {},
    GetAdminEventsIdPathParams
  >({ url: "/admin/events/{id}", method: "get", ...variables, signal });

export function getAdminEventsIdQuery(variables: GetAdminEventsIdVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetAdminEventsIdResponse>;
};

export function getAdminEventsIdQuery(
  variables: GetAdminEventsIdVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetAdminEventsIdResponse>)
    | reactQuery.SkipToken;
};

export function getAdminEventsIdQuery(
  variables: GetAdminEventsIdVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/admin/events/{id}",
      operationId: "getAdminEventsId",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetAdminEventsId(variables, signal),
  };
}

export const useSuspenseGetAdminEventsId = <TData = GetAdminEventsIdResponse,>(
  variables: GetAdminEventsIdVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminEventsIdResponse,
      GetAdminEventsIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetAdminEventsIdResponse,
    GetAdminEventsIdError,
    TData
  >({
    ...getAdminEventsIdQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAdminEventsId = <TData = GetAdminEventsIdResponse,>(
  variables: GetAdminEventsIdVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminEventsIdResponse,
      GetAdminEventsIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetAdminEventsIdResponse,
    GetAdminEventsIdError,
    TData
  >({
    ...getAdminEventsIdQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PatchAdminEventsIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type PatchAdminEventsIdError = Fetcher.ErrorWrapper<
  | {
      status: 400;
      payload: Schemas.ErrorResponse;
    }
  | {
      status: 404;
      payload: Schemas.ErrorResponse;
    }
>;

export type PatchAdminEventsIdRequestBody = {
  /**
   * @example Reitaisai 22
   */
  name_en?: string;
  /**
   * @example 第二十二回博麗神社例大祭
   */
  name_ja?: string;
  /**
   * @example 第二十二回博丽神社例大祭
   */
  name_zh?: string;
  /**
   * @example May 3, 2025 (Sat) 10:30 – 15:30
   */
  date_en?: string;
  /**
   * @example 2025年5月3日(土・祝) 10:30 – 15:30
   */
  date_ja?: string;
  /**
   * @example 2025年5月3日(周六) 10:30 – 15:30
   */
  date_zh?: string;
  /**
   * @example 20250503
   */
  date_sort?: number;
  image_url?: string | null;
  /**
   * @example tokyo-big-sight
   */
  venue_id?: string;
  url?: string | null;
};

export type PatchAdminEventsIdVariables = {
  body?: PatchAdminEventsIdRequestBody;
  pathParams: PatchAdminEventsIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchPatchAdminEventsId = (
  variables: PatchAdminEventsIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.SuccessResponse,
    PatchAdminEventsIdError,
    PatchAdminEventsIdRequestBody,
    {},
    {},
    PatchAdminEventsIdPathParams
  >({ url: "/admin/events/{id}", method: "patch", ...variables, signal });

export const usePatchAdminEventsId = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      Schemas.SuccessResponse,
      PatchAdminEventsIdError,
      PatchAdminEventsIdVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    Schemas.SuccessResponse,
    PatchAdminEventsIdError,
    PatchAdminEventsIdVariables
  >({
    mutationFn: (variables: PatchAdminEventsIdVariables) =>
      fetchPatchAdminEventsId(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type DeleteAdminEventsIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type DeleteAdminEventsIdError = Fetcher.ErrorWrapper<{
  status: 404;
  payload: Schemas.ErrorResponse;
}>;

export type DeleteAdminEventsIdResponse = {
  message: string;
};

export type DeleteAdminEventsIdVariables = {
  pathParams: DeleteAdminEventsIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchDeleteAdminEventsId = (
  variables: DeleteAdminEventsIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    DeleteAdminEventsIdResponse,
    DeleteAdminEventsIdError,
    undefined,
    {},
    {},
    DeleteAdminEventsIdPathParams
  >({ url: "/admin/events/{id}", method: "delete", ...variables, signal });

export const useDeleteAdminEventsId = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      DeleteAdminEventsIdResponse,
      DeleteAdminEventsIdError,
      DeleteAdminEventsIdVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    DeleteAdminEventsIdResponse,
    DeleteAdminEventsIdError,
    DeleteAdminEventsIdVariables
  >({
    mutationFn: (variables: DeleteAdminEventsIdVariables) =>
      fetchDeleteAdminEventsId(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetAdminCirclesError = Fetcher.ErrorWrapper<undefined>;

export type GetAdminCirclesResponse = {
  /**
   * @example 120
   */
  total: number;
  /**
   * @example 1
   */
  page: number;
  /**
   * @example 20
   */
  pageSize: number;
  items: {
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @example Reitaisai 22
     */
    name_en?: string;
    /**
     * @example 第二十二回博麗神社例大祭
     */
    name_ja: string;
    /**
     * @example 第二十二回博丽神社例大祭
     */
    name_zh: string;
    /**
     * @example May 3, 2025 (Sat) 10:30 – 15:30
     */
    date_en: string;
    /**
     * @example 2025年5月3日(土・祝) 10:30 – 15:30
     */
    date_ja: string;
    /**
     * @example 2025年5月3日(周六) 10:30 – 15:30
     */
    date_zh: string;
    /**
     * @example 20250503
     */
    date_sort?: number;
    image_url?: string | null;
    /**
     * @example tokyo-big-sight
     */
    venue_id: string;
    url?: string | null;
    /**
     * @example 2024-01-01T00:00:00Z
     */
    created_at?: string;
    /**
     * @example 2024-01-01T00:00:00Z
     */
    updated_at?: string;
    /**
     * @minLength 0
     * @example 東方愛好会
     */
    name: string;
    /**
     * @example {"author":"Alice","twitter_url":"https://twitter.com/example"}
     */
    urls?: string | null;
  }[];
};

export type GetAdminCirclesVariables = AyafeedContext["fetcherOptions"];

export const fetchGetAdminCircles = (
  variables: GetAdminCirclesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetAdminCirclesResponse,
    GetAdminCirclesError,
    undefined,
    {},
    {},
    {}
  >({ url: "/admin/circles", method: "get", ...variables, signal });

export function getAdminCirclesQuery(variables: GetAdminCirclesVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetAdminCirclesResponse>;
};

export function getAdminCirclesQuery(
  variables: GetAdminCirclesVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetAdminCirclesResponse>)
    | reactQuery.SkipToken;
};

export function getAdminCirclesQuery(
  variables: GetAdminCirclesVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/admin/circles",
      operationId: "getAdminCircles",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetAdminCircles(variables, signal),
  };
}

export const useSuspenseGetAdminCircles = <TData = GetAdminCirclesResponse,>(
  variables: GetAdminCirclesVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminCirclesResponse,
      GetAdminCirclesError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetAdminCirclesResponse,
    GetAdminCirclesError,
    TData
  >({
    ...getAdminCirclesQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAdminCircles = <TData = GetAdminCirclesResponse,>(
  variables: GetAdminCirclesVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminCirclesResponse,
      GetAdminCirclesError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetAdminCirclesResponse,
    GetAdminCirclesError,
    TData
  >({
    ...getAdminCirclesQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PostAdminCirclesError = Fetcher.ErrorWrapper<
  | {
      status: 400;
      payload: Schemas.ErrorResponse;
    }
  | {
      status: 409;
      payload: Schemas.ErrorResponse;
    }
>;

export type PostAdminCirclesRequestBody = {
  /**
   * @example 東方愛好会
   */
  name: string;
  /**
   * @example Alice
   */
  author?: string;
  /**
   * @example https://twitter.com/example
   */
  twitter_url?: string;
  /**
   * @example https://pixiv.net/users/123
   */
  pixiv_url?: string;
  /**
   * @example https://example.com
   */
  web_url?: string;
};

export type PostAdminCirclesVariables = {
  body: PostAdminCirclesRequestBody;
} & AyafeedContext["fetcherOptions"];

export const fetchPostAdminCircles = (
  variables: PostAdminCirclesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.SuccessResponse,
    PostAdminCirclesError,
    PostAdminCirclesRequestBody,
    {},
    {},
    {}
  >({ url: "/admin/circles", method: "post", ...variables, signal });

export const usePostAdminCircles = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      Schemas.SuccessResponse,
      PostAdminCirclesError,
      PostAdminCirclesVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    Schemas.SuccessResponse,
    PostAdminCirclesError,
    PostAdminCirclesVariables
  >({
    mutationFn: (variables: PostAdminCirclesVariables) =>
      fetchPostAdminCircles(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetAdminCirclesIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type GetAdminCirclesIdError = Fetcher.ErrorWrapper<{
  status: 404;
  payload: Schemas.ErrorResponse;
}>;

export type GetAdminCirclesIdResponse = {
  /**
   * @example uuid-123
   */
  id: string;
  /**
   * @minLength 0
   * @example 東方愛好会
   */
  name: string;
  /**
   * @example {"author":"Alice","twitter_url":"https://twitter.com/example"}
   */
  urls?: string | null;
  /**
   * @example 2024-01-01T00:00:00Z
   */
  created_at?: string;
  /**
   * @example 2024-01-01T00:00:00Z
   */
  updated_at?: string;
};

export type GetAdminCirclesIdVariables = {
  pathParams: GetAdminCirclesIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetAdminCirclesId = (
  variables: GetAdminCirclesIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetAdminCirclesIdResponse,
    GetAdminCirclesIdError,
    undefined,
    {},
    {},
    GetAdminCirclesIdPathParams
  >({ url: "/admin/circles/{id}", method: "get", ...variables, signal });

export function getAdminCirclesIdQuery(variables: GetAdminCirclesIdVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetAdminCirclesIdResponse>;
};

export function getAdminCirclesIdQuery(
  variables: GetAdminCirclesIdVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetAdminCirclesIdResponse>)
    | reactQuery.SkipToken;
};

export function getAdminCirclesIdQuery(
  variables: GetAdminCirclesIdVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/admin/circles/{id}",
      operationId: "getAdminCirclesId",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetAdminCirclesId(variables, signal),
  };
}

export const useSuspenseGetAdminCirclesId = <
  TData = GetAdminCirclesIdResponse,
>(
  variables: GetAdminCirclesIdVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminCirclesIdResponse,
      GetAdminCirclesIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetAdminCirclesIdResponse,
    GetAdminCirclesIdError,
    TData
  >({
    ...getAdminCirclesIdQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAdminCirclesId = <TData = GetAdminCirclesIdResponse,>(
  variables: GetAdminCirclesIdVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminCirclesIdResponse,
      GetAdminCirclesIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetAdminCirclesIdResponse,
    GetAdminCirclesIdError,
    TData
  >({
    ...getAdminCirclesIdQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PutAdminCirclesIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type PutAdminCirclesIdError = Fetcher.ErrorWrapper<
  | {
      status: 400;
      payload: Schemas.ErrorResponse;
    }
  | {
      status: 404;
      payload: Schemas.ErrorResponse;
    }
>;

export type PutAdminCirclesIdRequestBody = {
  name?: string;
  author?: string;
  twitter_url?: string;
  pixiv_url?: string;
  web_url?: string;
};

export type PutAdminCirclesIdVariables = {
  body?: PutAdminCirclesIdRequestBody;
  pathParams: PutAdminCirclesIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchPutAdminCirclesId = (
  variables: PutAdminCirclesIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.SuccessResponse,
    PutAdminCirclesIdError,
    PutAdminCirclesIdRequestBody,
    {},
    {},
    PutAdminCirclesIdPathParams
  >({ url: "/admin/circles/{id}", method: "put", ...variables, signal });

export const usePutAdminCirclesId = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      Schemas.SuccessResponse,
      PutAdminCirclesIdError,
      PutAdminCirclesIdVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    Schemas.SuccessResponse,
    PutAdminCirclesIdError,
    PutAdminCirclesIdVariables
  >({
    mutationFn: (variables: PutAdminCirclesIdVariables) =>
      fetchPutAdminCirclesId(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type DeleteAdminCirclesIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type DeleteAdminCirclesIdError = Fetcher.ErrorWrapper<{
  status: 404;
  payload: Schemas.ErrorResponse;
}>;

export type DeleteAdminCirclesIdResponse = {
  message: string;
};

export type DeleteAdminCirclesIdVariables = {
  pathParams: DeleteAdminCirclesIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchDeleteAdminCirclesId = (
  variables: DeleteAdminCirclesIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    DeleteAdminCirclesIdResponse,
    DeleteAdminCirclesIdError,
    undefined,
    {},
    {},
    DeleteAdminCirclesIdPathParams
  >({ url: "/admin/circles/{id}", method: "delete", ...variables, signal });

export const useDeleteAdminCirclesId = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      DeleteAdminCirclesIdResponse,
      DeleteAdminCirclesIdError,
      DeleteAdminCirclesIdVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    DeleteAdminCirclesIdResponse,
    DeleteAdminCirclesIdError,
    DeleteAdminCirclesIdVariables
  >({
    mutationFn: (variables: DeleteAdminCirclesIdVariables) =>
      fetchDeleteAdminCirclesId(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetAdminVenuesQueryParams = {
  /**
   * @example 1
   */
  page?: string;
  /**
   * @example 50
   */
  pageSize?: string;
  /**
   * @example Big Sight
   */
  keyword?: string;
  /**
   * @example Tokyo
   */
  city?: string;
  /**
   * @example 1000
   */
  capacity_min?: string;
  /**
   * @example 10000
   */
  capacity_max?: string;
  /**
   * @example true
   */
  has_parking?: string;
  /**
   * @example true
   */
  has_wifi?: string;
};

export type GetAdminVenuesError = Fetcher.ErrorWrapper<undefined>;

export type GetAdminVenuesResponse = {
  /**
   * @example 120
   */
  total: number;
  /**
   * @example 1
   */
  page: number;
  /**
   * @example 20
   */
  pageSize: number;
  items: {
    /**
     * @example tokyo-big-sight
     */
    id: string;
    /**
     * @example Tokyo Big Sight
     */
    name_en: string;
    /**
     * @example 東京ビッグサイト
     */
    name_ja: string;
    /**
     * @example 东京 Big Sight
     */
    name_zh: string;
    /**
     * @example May 3, 2025 (Sat) 10:30 – 15:30
     */
    date_en?: string;
    /**
     * @example 2025年5月3日(土・祝) 10:30 – 15:30
     */
    date_ja?: string;
    /**
     * @example 2025年5月3日(周六) 10:30 – 15:30
     */
    date_zh: string;
    /**
     * @example 20250503
     */
    date_sort?: number;
    image_url?: string | null;
    /**
     * @example tokyo-big-sight
     */
    venue_id: string;
    url?: string | null;
    created_at?: string;
    updated_at?: string;
    address_en?: string | null;
    address_ja?: string | null;
    address_zh?: string | null;
    /**
     * @example 35.6298
     */
    lat: number;
    /**
     * @example 139.793
     */
    lng: number;
    capacity?: number | null;
    website_url?: string | null;
    phone?: string | null;
    description_en?: string | null;
    description_ja?: string | null;
    description_zh?: string | null;
    facilities?: string | null;
    transportation?: string | null;
    parking_info?: string | null;
  }[];
};

export type GetAdminVenuesVariables = {
  queryParams?: GetAdminVenuesQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetAdminVenues = (
  variables: GetAdminVenuesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetAdminVenuesResponse,
    GetAdminVenuesError,
    undefined,
    {},
    GetAdminVenuesQueryParams,
    {}
  >({ url: "/admin/venues", method: "get", ...variables, signal });

export function getAdminVenuesQuery(variables: GetAdminVenuesVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetAdminVenuesResponse>;
};

export function getAdminVenuesQuery(
  variables: GetAdminVenuesVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetAdminVenuesResponse>)
    | reactQuery.SkipToken;
};

export function getAdminVenuesQuery(
  variables: GetAdminVenuesVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/admin/venues",
      operationId: "getAdminVenues",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetAdminVenues(variables, signal),
  };
}

export const useSuspenseGetAdminVenues = <TData = GetAdminVenuesResponse,>(
  variables: GetAdminVenuesVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminVenuesResponse,
      GetAdminVenuesError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetAdminVenuesResponse,
    GetAdminVenuesError,
    TData
  >({
    ...getAdminVenuesQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAdminVenues = <TData = GetAdminVenuesResponse,>(
  variables: GetAdminVenuesVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminVenuesResponse,
      GetAdminVenuesError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetAdminVenuesResponse,
    GetAdminVenuesError,
    TData
  >({
    ...getAdminVenuesQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PostAdminVenuesError = Fetcher.ErrorWrapper<{
  status: 400;
  payload: Schemas.ErrorResponse;
}>;

export type PostAdminVenuesResponse = {
  /**
   * @example tokyo-big-sight
   */
  id: string;
  /**
   * @example Tokyo Big Sight
   */
  name_en: string;
  /**
   * @example 東京ビッグサイト
   */
  name_ja: string;
  /**
   * @example 东京 Big Sight
   */
  name_zh: string;
  address_en?: string | null;
  address_ja?: string | null;
  address_zh?: string | null;
  /**
   * @example 35.6298
   */
  lat: number;
  /**
   * @example 139.793
   */
  lng: number;
  capacity?: number | null;
  website_url?: string | null;
  phone?: string | null;
  description_en?: string | null;
  description_ja?: string | null;
  description_zh?: string | null;
  facilities?: string | null;
  transportation?: string | null;
  parking_info?: string | null;
  created_at?: string;
  updated_at?: string;
};

export type PostAdminVenuesRequestBody = {
  /**
   * @example Tokyo Big Sight
   */
  name_en: string;
  /**
   * @example 東京ビッグサイト
   */
  name_ja: string;
  /**
   * @example 东京 Big Sight
   */
  name_zh: string;
  address_en?: string | null;
  address_ja?: string | null;
  address_zh?: string | null;
  /**
   * @example 35.6298
   */
  lat: number;
  /**
   * @example 139.793
   */
  lng: number;
  capacity?: number | null;
  website_url?: string | null;
  phone?: string | null;
  description_en?: string | null;
  description_ja?: string | null;
  description_zh?: string | null;
  facilities?: string | null;
  transportation?: string | null;
  parking_info?: string | null;
};

export type PostAdminVenuesVariables = {
  body: PostAdminVenuesRequestBody;
} & AyafeedContext["fetcherOptions"];

export const fetchPostAdminVenues = (
  variables: PostAdminVenuesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    PostAdminVenuesResponse,
    PostAdminVenuesError,
    PostAdminVenuesRequestBody,
    {},
    {},
    {}
  >({ url: "/admin/venues", method: "post", ...variables, signal });

export const usePostAdminVenues = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      PostAdminVenuesResponse,
      PostAdminVenuesError,
      PostAdminVenuesVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    PostAdminVenuesResponse,
    PostAdminVenuesError,
    PostAdminVenuesVariables
  >({
    mutationFn: (variables: PostAdminVenuesVariables) =>
      fetchPostAdminVenues(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetAdminVenuesIdPathParams = {
  id: string;
};

export type GetAdminVenuesIdError = Fetcher.ErrorWrapper<{
  status: 404;
  payload: Schemas.ErrorResponse;
}>;

export type GetAdminVenuesIdResponse = {
  /**
   * @example tokyo-big-sight
   */
  id: string;
  /**
   * @example Tokyo Big Sight
   */
  name_en: string;
  /**
   * @example 東京ビッグサイト
   */
  name_ja: string;
  /**
   * @example 东京 Big Sight
   */
  name_zh: string;
  address_en?: string | null;
  address_ja?: string | null;
  address_zh?: string | null;
  /**
   * @example 35.6298
   */
  lat: number;
  /**
   * @example 139.793
   */
  lng: number;
  capacity?: number | null;
  website_url?: string | null;
  phone?: string | null;
  description_en?: string | null;
  description_ja?: string | null;
  description_zh?: string | null;
  facilities?: string | null;
  transportation?: string | null;
  parking_info?: string | null;
  created_at?: string;
  updated_at?: string;
};

export type GetAdminVenuesIdVariables = {
  pathParams: GetAdminVenuesIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetAdminVenuesId = (
  variables: GetAdminVenuesIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetAdminVenuesIdResponse,
    GetAdminVenuesIdError,
    undefined,
    {},
    {},
    GetAdminVenuesIdPathParams
  >({ url: "/admin/venues/{id}", method: "get", ...variables, signal });

export function getAdminVenuesIdQuery(variables: GetAdminVenuesIdVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetAdminVenuesIdResponse>;
};

export function getAdminVenuesIdQuery(
  variables: GetAdminVenuesIdVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetAdminVenuesIdResponse>)
    | reactQuery.SkipToken;
};

export function getAdminVenuesIdQuery(
  variables: GetAdminVenuesIdVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/admin/venues/{id}",
      operationId: "getAdminVenuesId",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetAdminVenuesId(variables, signal),
  };
}

export const useSuspenseGetAdminVenuesId = <TData = GetAdminVenuesIdResponse,>(
  variables: GetAdminVenuesIdVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminVenuesIdResponse,
      GetAdminVenuesIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetAdminVenuesIdResponse,
    GetAdminVenuesIdError,
    TData
  >({
    ...getAdminVenuesIdQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAdminVenuesId = <TData = GetAdminVenuesIdResponse,>(
  variables: GetAdminVenuesIdVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminVenuesIdResponse,
      GetAdminVenuesIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetAdminVenuesIdResponse,
    GetAdminVenuesIdError,
    TData
  >({
    ...getAdminVenuesIdQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PutAdminVenuesIdPathParams = {
  id: string;
};

export type PutAdminVenuesIdError = Fetcher.ErrorWrapper<{
  status: 404;
  payload: Schemas.ErrorResponse;
}>;

export type PutAdminVenuesIdRequestBody = {
  /**
   * @example Tokyo Big Sight
   */
  name_en?: string;
  /**
   * @example 東京ビッグサイト
   */
  name_ja?: string;
  /**
   * @example 东京 Big Sight
   */
  name_zh?: string;
  address_en?: string | null;
  address_ja?: string | null;
  address_zh?: string | null;
  /**
   * @example 35.6298
   */
  lat?: number;
  /**
   * @example 139.793
   */
  lng?: number;
  capacity?: number | null;
  website_url?: string | null;
  phone?: string | null;
  description_en?: string | null;
  description_ja?: string | null;
  description_zh?: string | null;
  facilities?: string | null;
  transportation?: string | null;
  parking_info?: string | null;
};

export type PutAdminVenuesIdVariables = {
  body?: PutAdminVenuesIdRequestBody;
  pathParams: PutAdminVenuesIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchPutAdminVenuesId = (
  variables: PutAdminVenuesIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.SuccessResponse,
    PutAdminVenuesIdError,
    PutAdminVenuesIdRequestBody,
    {},
    {},
    PutAdminVenuesIdPathParams
  >({ url: "/admin/venues/{id}", method: "put", ...variables, signal });

export const usePutAdminVenuesId = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      Schemas.SuccessResponse,
      PutAdminVenuesIdError,
      PutAdminVenuesIdVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    Schemas.SuccessResponse,
    PutAdminVenuesIdError,
    PutAdminVenuesIdVariables
  >({
    mutationFn: (variables: PutAdminVenuesIdVariables) =>
      fetchPutAdminVenuesId(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type DeleteAdminVenuesIdPathParams = {
  id: string;
};

export type DeleteAdminVenuesIdError = Fetcher.ErrorWrapper<
  | {
      status: 400;
      payload: Schemas.ErrorResponse;
    }
  | {
      status: 404;
      payload: Schemas.ErrorResponse;
    }
>;

export type DeleteAdminVenuesIdVariables = {
  pathParams: DeleteAdminVenuesIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchDeleteAdminVenuesId = (
  variables: DeleteAdminVenuesIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.SuccessResponse,
    DeleteAdminVenuesIdError,
    undefined,
    {},
    {},
    DeleteAdminVenuesIdPathParams
  >({ url: "/admin/venues/{id}", method: "delete", ...variables, signal });

export const useDeleteAdminVenuesId = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      Schemas.SuccessResponse,
      DeleteAdminVenuesIdError,
      DeleteAdminVenuesIdVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    Schemas.SuccessResponse,
    DeleteAdminVenuesIdError,
    DeleteAdminVenuesIdVariables
  >({
    mutationFn: (variables: DeleteAdminVenuesIdVariables) =>
      fetchDeleteAdminVenuesId(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type PostAdminImagesUploadError = Fetcher.ErrorWrapper<{
  status: 400;
  payload: Schemas.ErrorResponse;
}>;

export type PostAdminImagesUploadRequestBody = {
  /**
   * 图片文件
   *
   * @format binary
   */
  file?: Blob;
  /**
   * 图片分类
   *
   * @example event
   */
  category: "event" | "circle" | "venue";
  /**
   * 关联的资源ID
   *
   * @example resource-uuid-789
   */
  resourceId: string;
  /**
   * 图片类型
   *
   * @example poster
   */
  imageType: "poster" | "logo" | "banner" | "gallery";
  /**
   * 图片变体
   *
   * @example thumb
   */
  variant: "original" | "large" | "medium" | "thumb";
  /**
   * 关联同一组图片的标识，可选
   *
   * @example group-uuid-456
   */
  groupId?: string;
};

export type PostAdminImagesUploadVariables = {
  body: PostAdminImagesUploadRequestBody;
} & AyafeedContext["fetcherOptions"];

export const fetchPostAdminImagesUpload = (
  variables: PostAdminImagesUploadVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.SuccessResponse,
    PostAdminImagesUploadError,
    PostAdminImagesUploadRequestBody,
    {},
    {},
    {}
  >({ url: "/admin/images/upload", method: "post", ...variables, signal });

export const usePostAdminImagesUpload = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      Schemas.SuccessResponse,
      PostAdminImagesUploadError,
      PostAdminImagesUploadVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    Schemas.SuccessResponse,
    PostAdminImagesUploadError,
    PostAdminImagesUploadVariables
  >({
    mutationFn: (variables: PostAdminImagesUploadVariables) =>
      fetchPostAdminImagesUpload(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type DeleteAdminImagesError = Fetcher.ErrorWrapper<{
  status: 400;
  payload: Schemas.ErrorResponse;
}>;

export type DeleteAdminImagesRequestBody = {
  /**
   * 要删除的图片相对路径列表
   *
   * @example /images/events/reitaisai-22/poster_thumb.jpg
   */
  relativePaths: string[];
};

export type DeleteAdminImagesVariables = {
  body: DeleteAdminImagesRequestBody;
} & AyafeedContext["fetcherOptions"];

export const fetchDeleteAdminImages = (
  variables: DeleteAdminImagesVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.SuccessResponse,
    DeleteAdminImagesError,
    DeleteAdminImagesRequestBody,
    {},
    {},
    {}
  >({ url: "/admin/images", method: "delete", ...variables, signal });

export const useDeleteAdminImages = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      Schemas.SuccessResponse,
      DeleteAdminImagesError,
      DeleteAdminImagesVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    Schemas.SuccessResponse,
    DeleteAdminImagesError,
    DeleteAdminImagesVariables
  >({
    mutationFn: (variables: DeleteAdminImagesVariables) =>
      fetchDeleteAdminImages(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetAdminImagesIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type GetAdminImagesIdError = Fetcher.ErrorWrapper<{
  status: 404;
  payload: Schemas.ErrorResponse;
}>;

export type GetAdminImagesIdVariables = {
  pathParams: GetAdminImagesIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetAdminImagesId = (
  variables: GetAdminImagesIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.SuccessResponse,
    GetAdminImagesIdError,
    undefined,
    {},
    {},
    GetAdminImagesIdPathParams
  >({ url: "/admin/images/{id}", method: "get", ...variables, signal });

export function getAdminImagesIdQuery(variables: GetAdminImagesIdVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<Schemas.SuccessResponse>;
};

export function getAdminImagesIdQuery(
  variables: GetAdminImagesIdVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<Schemas.SuccessResponse>)
    | reactQuery.SkipToken;
};

export function getAdminImagesIdQuery(
  variables: GetAdminImagesIdVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/admin/images/{id}",
      operationId: "getAdminImagesId",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetAdminImagesId(variables, signal),
  };
}

export const useSuspenseGetAdminImagesId = <TData = Schemas.SuccessResponse,>(
  variables: GetAdminImagesIdVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      Schemas.SuccessResponse,
      GetAdminImagesIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    Schemas.SuccessResponse,
    GetAdminImagesIdError,
    TData
  >({
    ...getAdminImagesIdQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAdminImagesId = <TData = Schemas.SuccessResponse,>(
  variables: GetAdminImagesIdVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      Schemas.SuccessResponse,
      GetAdminImagesIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    Schemas.SuccessResponse,
    GetAdminImagesIdError,
    TData
  >({
    ...getAdminImagesIdQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetAdminUsersError = Fetcher.ErrorWrapper<{
  status: 400;
  payload: Schemas.ErrorResponse;
}>;

export type GetAdminUsersResponse = {
  /**
   * @example 120
   */
  total: number;
  /**
   * @example 1
   */
  page: number;
  /**
   * @example 20
   */
  pageSize: number;
  items: {
    /**
     * @example uuid-123
     */
    id: string;
    /**
     * @example Reitaisai 22
     */
    name_en?: string;
    /**
     * @example 第二十二回博麗神社例大祭
     */
    name_ja?: string;
    /**
     * @example 第二十二回博丽神社例大祭
     */
    name_zh: string;
    /**
     * @example May 3, 2025 (Sat) 10:30 – 15:30
     */
    date_en: string;
    /**
     * @example 2025年5月3日(土・祝) 10:30 – 15:30
     */
    date_ja: string;
    /**
     * @example 2025年5月3日(周六) 10:30 – 15:30
     */
    date_zh: string;
    /**
     * @example 20250503
     */
    date_sort?: number;
    image_url?: string | null;
    /**
     * @example tokyo-big-sight
     */
    venue_id: string;
    url?: string | null;
    created_at?: string;
    updated_at?: string;
    /**
     * @example alice
     */
    username: string;
    /**
     * @example viewer
     */
    role: string;
  }[];
};

export type GetAdminUsersVariables = AyafeedContext["fetcherOptions"];

export const fetchGetAdminUsers = (
  variables: GetAdminUsersVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetAdminUsersResponse,
    GetAdminUsersError,
    undefined,
    {},
    {},
    {}
  >({ url: "/admin/users", method: "get", ...variables, signal });

export function getAdminUsersQuery(variables: GetAdminUsersVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetAdminUsersResponse>;
};

export function getAdminUsersQuery(
  variables: GetAdminUsersVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetAdminUsersResponse>)
    | reactQuery.SkipToken;
};

export function getAdminUsersQuery(
  variables: GetAdminUsersVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/admin/users",
      operationId: "getAdminUsers",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetAdminUsers(variables, signal),
  };
}

export const useSuspenseGetAdminUsers = <TData = GetAdminUsersResponse,>(
  variables: GetAdminUsersVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminUsersResponse,
      GetAdminUsersError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetAdminUsersResponse,
    GetAdminUsersError,
    TData
  >({
    ...getAdminUsersQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAdminUsers = <TData = GetAdminUsersResponse,>(
  variables: GetAdminUsersVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminUsersResponse,
      GetAdminUsersError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<GetAdminUsersResponse, GetAdminUsersError, TData>({
    ...getAdminUsersQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PostAdminUsersError = Fetcher.ErrorWrapper<{
  status: 400;
  payload: Schemas.ErrorResponse;
}>;

export type PostAdminUsersRequestBody = {
  /**
   * @minLength 1
   * @example alice
   */
  username: string;
  /**
   * @minLength 8
   * @example pwd12345
   */
  password: string;
  /**
   * @example viewer
   */
  role?: string;
};

export type PostAdminUsersVariables = {
  body: PostAdminUsersRequestBody;
} & AyafeedContext["fetcherOptions"];

export const fetchPostAdminUsers = (
  variables: PostAdminUsersVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.SuccessResponse,
    PostAdminUsersError,
    PostAdminUsersRequestBody,
    {},
    {},
    {}
  >({ url: "/admin/users", method: "post", ...variables, signal });

export const usePostAdminUsers = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      Schemas.SuccessResponse,
      PostAdminUsersError,
      PostAdminUsersVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    Schemas.SuccessResponse,
    PostAdminUsersError,
    PostAdminUsersVariables
  >({
    mutationFn: (variables: PostAdminUsersVariables) =>
      fetchPostAdminUsers(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetAdminUsersIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type GetAdminUsersIdError = Fetcher.ErrorWrapper<{
  status: 404;
  payload: Schemas.ErrorResponse;
}>;

export type GetAdminUsersIdResponse = {
  /**
   * @example uuid-123
   */
  id: string;
  /**
   * @example alice
   */
  username: string;
  /**
   * @example viewer
   */
  role: string;
};

export type GetAdminUsersIdVariables = {
  pathParams: GetAdminUsersIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetAdminUsersId = (
  variables: GetAdminUsersIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetAdminUsersIdResponse,
    GetAdminUsersIdError,
    undefined,
    {},
    {},
    GetAdminUsersIdPathParams
  >({ url: "/admin/users/{id}", method: "get", ...variables, signal });

export function getAdminUsersIdQuery(variables: GetAdminUsersIdVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetAdminUsersIdResponse>;
};

export function getAdminUsersIdQuery(
  variables: GetAdminUsersIdVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetAdminUsersIdResponse>)
    | reactQuery.SkipToken;
};

export function getAdminUsersIdQuery(
  variables: GetAdminUsersIdVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/admin/users/{id}",
      operationId: "getAdminUsersId",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) =>
            fetchGetAdminUsersId(variables, signal),
  };
}

export const useSuspenseGetAdminUsersId = <TData = GetAdminUsersIdResponse,>(
  variables: GetAdminUsersIdVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminUsersIdResponse,
      GetAdminUsersIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetAdminUsersIdResponse,
    GetAdminUsersIdError,
    TData
  >({
    ...getAdminUsersIdQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAdminUsersId = <TData = GetAdminUsersIdResponse,>(
  variables: GetAdminUsersIdVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminUsersIdResponse,
      GetAdminUsersIdError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<
    GetAdminUsersIdResponse,
    GetAdminUsersIdError,
    TData
  >({
    ...getAdminUsersIdQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type PutAdminUsersIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type PutAdminUsersIdError = Fetcher.ErrorWrapper<{
  status: 400;
  payload: Schemas.ErrorResponse;
}>;

export type PutAdminUsersIdRequestBody = {
  /**
   * @example alice
   */
  username?: string;
  /**
   * @example editor
   */
  role?: string;
  /**
   * @example newpassword
   */
  password?: string;
};

export type PutAdminUsersIdVariables = {
  body?: PutAdminUsersIdRequestBody;
  pathParams: PutAdminUsersIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchPutAdminUsersId = (
  variables: PutAdminUsersIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    Schemas.SuccessResponse,
    PutAdminUsersIdError,
    PutAdminUsersIdRequestBody,
    {},
    {},
    PutAdminUsersIdPathParams
  >({ url: "/admin/users/{id}", method: "put", ...variables, signal });

export const usePutAdminUsersId = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      Schemas.SuccessResponse,
      PutAdminUsersIdError,
      PutAdminUsersIdVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    Schemas.SuccessResponse,
    PutAdminUsersIdError,
    PutAdminUsersIdVariables
  >({
    mutationFn: (variables: PutAdminUsersIdVariables) =>
      fetchPutAdminUsersId(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type DeleteAdminUsersIdPathParams = {
  /**
   * @example uuid-123
   */
  id: string;
};

export type DeleteAdminUsersIdError = Fetcher.ErrorWrapper<undefined>;

export type DeleteAdminUsersIdResponse = {
  message: string;
};

export type DeleteAdminUsersIdVariables = {
  pathParams: DeleteAdminUsersIdPathParams;
} & AyafeedContext["fetcherOptions"];

export const fetchDeleteAdminUsersId = (
  variables: DeleteAdminUsersIdVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    DeleteAdminUsersIdResponse,
    DeleteAdminUsersIdError,
    undefined,
    {},
    {},
    DeleteAdminUsersIdPathParams
  >({ url: "/admin/users/{id}", method: "delete", ...variables, signal });

export const useDeleteAdminUsersId = (
  options?: Omit<
    reactQuery.UseMutationOptions<
      DeleteAdminUsersIdResponse,
      DeleteAdminUsersIdError,
      DeleteAdminUsersIdVariables
    >,
    "mutationFn"
  >,
) => {
  const { fetcherOptions } = useAyafeedContext();
  return reactQuery.useMutation<
    DeleteAdminUsersIdResponse,
    DeleteAdminUsersIdError,
    DeleteAdminUsersIdVariables
  >({
    mutationFn: (variables: DeleteAdminUsersIdVariables) =>
      fetchDeleteAdminUsersId(deepMerge(fetcherOptions, variables)),
    ...options,
  });
};

export type GetAdminLogsError = Fetcher.ErrorWrapper<undefined>;

export type GetAdminLogsVariables = AyafeedContext["fetcherOptions"];

export const fetchGetAdminLogs = (
  variables: GetAdminLogsVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<undefined, GetAdminLogsError, undefined, {}, {}, {}>({
    url: "/admin/logs",
    method: "get",
    ...variables,
    signal,
  });

export function getAdminLogsQuery(variables: GetAdminLogsVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<undefined>;
};

export function getAdminLogsQuery(
  variables: GetAdminLogsVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<undefined>)
    | reactQuery.SkipToken;
};

export function getAdminLogsQuery(
  variables: GetAdminLogsVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/admin/logs",
      operationId: "getAdminLogs",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetAdminLogs(variables, signal),
  };
}

export const useSuspenseGetAdminLogs = <TData = undefined,>(
  variables: GetAdminLogsVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<undefined, GetAdminLogsError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<undefined, GetAdminLogsError, TData>({
    ...getAdminLogsQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAdminLogs = <TData = undefined,>(
  variables: GetAdminLogsVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<undefined, GetAdminLogsError, TData>,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<undefined, GetAdminLogsError, TData>({
    ...getAdminLogsQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type GetAdminStatsQueryParams = {
  /**
   * @example 2025
   */
  year?: string;
};

export type GetAdminStatsError = Fetcher.ErrorWrapper<undefined>;

export type GetAdminStatsResponse = {
  totals: {
    /**
     * @example 123
     */
    circles: number;
    /**
     * @example 456
     */
    artists: number;
    /**
     * @example 78
     */
    events: number;
  };
  /**
   * @example 2025
   */
  year: number;
  eventsByMonth: {
    /**
     * @example 01
     */
    month: string;
    /**
     * @example 12
     */
    count: number;
  }[];
};

export type GetAdminStatsVariables = {
  queryParams?: GetAdminStatsQueryParams;
} & AyafeedContext["fetcherOptions"];

export const fetchGetAdminStats = (
  variables: GetAdminStatsVariables,
  signal?: AbortSignal,
) =>
  ayafeedFetch<
    GetAdminStatsResponse,
    GetAdminStatsError,
    undefined,
    {},
    GetAdminStatsQueryParams,
    {}
  >({ url: "/admin/stats", method: "get", ...variables, signal });

export function getAdminStatsQuery(variables: GetAdminStatsVariables): {
  queryKey: reactQuery.QueryKey;
  queryFn: (options: QueryFnOptions) => Promise<GetAdminStatsResponse>;
};

export function getAdminStatsQuery(
  variables: GetAdminStatsVariables | reactQuery.SkipToken,
): {
  queryKey: reactQuery.QueryKey;
  queryFn:
    | ((options: QueryFnOptions) => Promise<GetAdminStatsResponse>)
    | reactQuery.SkipToken;
};

export function getAdminStatsQuery(
  variables: GetAdminStatsVariables | reactQuery.SkipToken,
) {
  return {
    queryKey: queryKeyFn({
      path: "/admin/stats",
      operationId: "getAdminStats",
      variables,
    }),
    queryFn:
      variables === reactQuery.skipToken
        ? reactQuery.skipToken
        : ({ signal }: QueryFnOptions) => fetchGetAdminStats(variables, signal),
  };
}

export const useSuspenseGetAdminStats = <TData = GetAdminStatsResponse,>(
  variables: GetAdminStatsVariables,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminStatsResponse,
      GetAdminStatsError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useSuspenseQuery<
    GetAdminStatsResponse,
    GetAdminStatsError,
    TData
  >({
    ...getAdminStatsQuery(deepMerge(fetcherOptions, variables)),
    ...options,
    ...queryOptions,
  });
};

export const useGetAdminStats = <TData = GetAdminStatsResponse,>(
  variables: GetAdminStatsVariables | reactQuery.SkipToken,
  options?: Omit<
    reactQuery.UseQueryOptions<
      GetAdminStatsResponse,
      GetAdminStatsError,
      TData
    >,
    "queryKey" | "queryFn" | "initialData"
  >,
) => {
  const { queryOptions, fetcherOptions } = useAyafeedContext(options);
  return reactQuery.useQuery<GetAdminStatsResponse, GetAdminStatsError, TData>({
    ...getAdminStatsQuery(
      variables === reactQuery.skipToken
        ? variables
        : deepMerge(fetcherOptions, variables),
    ),
    ...options,
    ...queryOptions,
  });
};

export type QueryOperation =
  | {
      path: "/auth/get-session";
      operationId: "getAuthGetsession";
      variables: GetAuthGetsessionVariables | reactQuery.SkipToken;
    }
  | {
      path: "/events";
      operationId: "getEvents";
      variables: GetEventsVariables | reactQuery.SkipToken;
    }
  | {
      path: "/events/{id}";
      operationId: "getEventsId";
      variables: GetEventsIdVariables | reactQuery.SkipToken;
    }
  | {
      path: "/events/{id}/circles";
      operationId: "getEventsIdCircles";
      variables: GetEventsIdCirclesVariables | reactQuery.SkipToken;
    }
  | {
      path: "/events/{id}/appearances";
      operationId: "getEventsIdAppearances";
      variables: GetEventsIdAppearancesVariables | reactQuery.SkipToken;
    }
  | {
      path: "/venues";
      operationId: "getVenues";
      variables: GetVenuesVariables | reactQuery.SkipToken;
    }
  | {
      path: "/venues/{id}";
      operationId: "getVenuesId";
      variables: GetVenuesIdVariables | reactQuery.SkipToken;
    }
  | {
      path: "/circles";
      operationId: "getCircles";
      variables: GetCirclesVariables | reactQuery.SkipToken;
    }
  | {
      path: "/circles/{id}";
      operationId: "getCirclesId";
      variables: GetCirclesIdVariables | reactQuery.SkipToken;
    }
  | {
      path: "/circles/{id}/appearances";
      operationId: "getCirclesIdAppearances";
      variables: GetCirclesIdAppearancesVariables | reactQuery.SkipToken;
    }
  | {
      path: "/circles/{circleId}/bookmark/status";
      operationId: "getCirclesCircleIdBookmarkStatus";
      variables:
        | GetCirclesCircleIdBookmarkStatusVariables
        | reactQuery.SkipToken;
    }
  | {
      path: "/user/bookmarks";
      operationId: "getUserBookmarks";
      variables: GetUserBookmarksVariables | reactQuery.SkipToken;
    }
  | {
      path: "/user/bookmarks/stats";
      operationId: "getUserBookmarksStats";
      variables: GetUserBookmarksStatsVariables | reactQuery.SkipToken;
    }
  | {
      path: "/artists";
      operationId: "getArtists";
      variables: GetArtistsVariables | reactQuery.SkipToken;
    }
  | {
      path: "/artists/{id}";
      operationId: "getArtistsId";
      variables: GetArtistsIdVariables | reactQuery.SkipToken;
    }
  | {
      path: "/appearances";
      operationId: "getAppearances";
      variables: GetAppearancesVariables | reactQuery.SkipToken;
    }
  | {
      path: "/appearances/{id}";
      operationId: "getAppearancesId";
      variables: GetAppearancesIdVariables | reactQuery.SkipToken;
    }
  | {
      path: "/search";
      operationId: "getSearch";
      variables: GetSearchVariables | reactQuery.SkipToken;
    }
  | {
      path: "/feed";
      operationId: "getFeed";
      variables: GetFeedVariables | reactQuery.SkipToken;
    }
  | {
      path: "/images/batch";
      operationId: "getImagesBatch";
      variables: GetImagesBatchVariables | reactQuery.SkipToken;
    }
  | {
      path: "/images/{id}/file";
      operationId: "getImagesIdFile";
      variables: GetImagesIdFileVariables | reactQuery.SkipToken;
    }
  | {
      path: "/images/{id}";
      operationId: "getImagesId";
      variables: GetImagesIdVariables | reactQuery.SkipToken;
    }
  | {
      path: "/images/{category}/{resourceId}";
      operationId: "getImagesCategoryResourceId";
      variables: GetImagesCategoryResourceIdVariables | reactQuery.SkipToken;
    }
  | {
      path: "/rich-text/{entityType}/{entityId}/content";
      operationId: "getRichtextEntityTypeEntityIdContent";
      variables:
        | GetRichtextEntityTypeEntityIdContentVariables
        | reactQuery.SkipToken;
    }
  | {
      path: "/rich-text/{entityType}/{entityId}/content/{contentType}";
      operationId: "getRichtextEntityTypeEntityIdContentContentType";
      variables:
        | GetRichtextEntityTypeEntityIdContentContentTypeVariables
        | reactQuery.SkipToken;
    }
  | {
      path: "/rich-text-tabs/configs/{entityType}/{languageCode}";
      operationId: "getRichtexttabsConfigsEntityTypeLanguageCode";
      variables:
        | GetRichtexttabsConfigsEntityTypeLanguageCodeVariables
        | reactQuery.SkipToken;
    }
  | {
      path: "/rich-text-tabs/configs/{entityType}/{languageCode}/all";
      operationId: "getRichtexttabsConfigsEntityTypeLanguageCodeAll";
      variables:
        | GetRichtexttabsConfigsEntityTypeLanguageCodeAllVariables
        | reactQuery.SkipToken;
    }
  | {
      path: "/rich-text-tabs/configs/suggest-keys";
      operationId: "getRichtexttabsConfigsSuggestkeys";
      variables:
        | GetRichtexttabsConfigsSuggestkeysVariables
        | reactQuery.SkipToken;
    }
  | {
      path: "/rich-text-tabs/tabs/{entityType}/{entityId}/{languageCode}";
      operationId: "getRichtexttabsTabsEntityTypeEntityIdLanguageCode";
      variables:
        | GetRichtexttabsTabsEntityTypeEntityIdLanguageCodeVariables
        | reactQuery.SkipToken;
    }
  | {
      path: "/rich-text-tabs/health/preset-configs";
      operationId: "getRichtexttabsHealthPresetconfigs";
      variables:
        | GetRichtexttabsHealthPresetconfigsVariables
        | reactQuery.SkipToken;
    }
  | {
      path: "/admin/events";
      operationId: "getAdminEvents";
      variables: GetAdminEventsVariables | reactQuery.SkipToken;
    }
  | {
      path: "/admin/events/{id}";
      operationId: "getAdminEventsId";
      variables: GetAdminEventsIdVariables | reactQuery.SkipToken;
    }
  | {
      path: "/admin/circles";
      operationId: "getAdminCircles";
      variables: GetAdminCirclesVariables | reactQuery.SkipToken;
    }
  | {
      path: "/admin/circles/{id}";
      operationId: "getAdminCirclesId";
      variables: GetAdminCirclesIdVariables | reactQuery.SkipToken;
    }
  | {
      path: "/admin/venues";
      operationId: "getAdminVenues";
      variables: GetAdminVenuesVariables | reactQuery.SkipToken;
    }
  | {
      path: "/admin/venues/{id}";
      operationId: "getAdminVenuesId";
      variables: GetAdminVenuesIdVariables | reactQuery.SkipToken;
    }
  | {
      path: "/admin/images/{id}";
      operationId: "getAdminImagesId";
      variables: GetAdminImagesIdVariables | reactQuery.SkipToken;
    }
  | {
      path: "/admin/users";
      operationId: "getAdminUsers";
      variables: GetAdminUsersVariables | reactQuery.SkipToken;
    }
  | {
      path: "/admin/users/{id}";
      operationId: "getAdminUsersId";
      variables: GetAdminUsersIdVariables | reactQuery.SkipToken;
    }
  | {
      path: "/admin/logs";
      operationId: "getAdminLogs";
      variables: GetAdminLogsVariables | reactQuery.SkipToken;
    }
  | {
      path: "/admin/stats";
      operationId: "getAdminStats";
      variables: GetAdminStatsVariables | reactQuery.SkipToken;
    };
