/* Rich Text Editor Styles */

.rich-text-editor {
  @apply w-full;
}

.rich-text-content {
  @apply w-full;
}

/* Editor Content Styles */
.ProseMirror {
  @apply outline-none min-h-[200px] p-4 border border-gray-200 rounded-b-md;
}

.ProseMirror:focus {
  @apply border-blue-500 ring-1 ring-blue-500;
}

/* Placeholder - 使用官方 @tiptap/extension-placeholder */
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  @apply text-gray-400 pointer-events-none float-left h-0;
}

/* Typography */
.ProseMirror h1 {
  @apply text-3xl font-bold mb-4 mt-6 first:mt-0;
}

.ProseMirror h2 {
  @apply text-2xl font-bold mb-3 mt-5 first:mt-0;
}

.ProseMirror h3 {
  @apply text-xl font-bold mb-2 mt-4 first:mt-0;
}

.ProseMirror p {
  @apply mb-3 last:mb-0;
}

/* Lists */
.ProseMirror .rich-text-bullet-list,
.ProseMirror .rich-text-ordered-list {
  @apply pl-6 mb-3;
}

.ProseMirror .rich-text-bullet-list li {
  @apply list-disc mb-1;
}

.ProseMirror .rich-text-ordered-list li {
  @apply list-decimal mb-1;
}

.ProseMirror li p {
  @apply mb-1;
}

/* Nested lists */
.ProseMirror .rich-text-bullet-list .rich-text-bullet-list,
.ProseMirror .rich-text-ordered-list .rich-text-ordered-list,
.ProseMirror .rich-text-bullet-list .rich-text-ordered-list,
.ProseMirror .rich-text-ordered-list .rich-text-bullet-list {
  @apply mt-1 mb-1;
}

/* Blockquotes */
.ProseMirror blockquote {
  @apply border-l-4 border-gray-300 pl-4 py-2 mb-3 italic text-gray-700 bg-gray-50;
}

/* Code */
.ProseMirror code {
  @apply bg-gray-100 px-1 py-0.5 rounded text-sm font-mono;
}

.ProseMirror pre {
  @apply bg-gray-100 p-4 rounded mb-3 overflow-x-auto;
}

.ProseMirror pre code {
  @apply bg-transparent p-0;
}

/* Links */
.ProseMirror .rich-text-link {
  @apply text-blue-600 underline hover:text-blue-800 cursor-pointer;
}

/* Images */
.ProseMirror .rich-text-image {
  @apply max-w-full h-auto rounded border border-gray-200 my-2;
}

/* Text alignment */
.ProseMirror [data-text-align="left"] {
  @apply text-left;
}

.ProseMirror [data-text-align="center"] {
  @apply text-center;
}

.ProseMirror [data-text-align="right"] {
  @apply text-right;
}

/* Selection */
.ProseMirror ::selection {
  @apply bg-blue-200;
}

/* Focus styles */
.ProseMirror:focus-within {
  @apply border-blue-500 ring-1 ring-blue-500;
}

/* Toolbar styles */
.rich-text-toolbar {
  @apply border border-gray-200 rounded-t-md p-2 bg-gray-50 flex flex-wrap gap-1 items-center;
}

/* Responsive adjustments */
@media (max-width: 640px) {
  .ProseMirror {
    @apply p-3 text-sm;
  }
  
  .ProseMirror h1 {
    @apply text-2xl;
  }
  
  .ProseMirror h2 {
    @apply text-xl;
  }
  
  .ProseMirror h3 {
    @apply text-lg;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  .ProseMirror {
    @apply bg-gray-800 text-white border-gray-600;
  }
  
  .ProseMirror:focus {
    @apply border-blue-400 ring-blue-400;
  }
  
  .ProseMirror blockquote {
    @apply border-gray-600 text-gray-300 bg-gray-700;
  }
  
  .ProseMirror code {
    @apply bg-gray-700 text-gray-200;
  }
  
  .ProseMirror pre {
    @apply bg-gray-700;
  }
  
  .ProseMirror .rich-text-link {
    @apply text-blue-400 hover:text-blue-300;
  }
  
  .ProseMirror .rich-text-image {
    @apply border-gray-600;
  }
  
  .ProseMirror ::selection {
    @apply bg-blue-800;
  }
}
