'use client';

import { useState } from 'react';
import * as Tabs from "@radix-ui/react-tabs";
import {
  FileText,
  Globe,
  Save,
  Loader2,
  AlertCircle,
  Plus,
  Settings,
  Edit,
  Trash2,
  SaveAll
} from 'lucide-react';
import { cn } from "@/lib/utils";
import { RadixCard, RadixCardHeader, RadixCardTitle, RadixCardDescription, RadixCardContent } from '@/components/ui/radix-components';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';

import { RichTextEditor } from '../rich-text-editor/RichTextEditor';
import { useRichTextTabs, type EntityType, type LanguageCode, type TabConfig } from '@/hooks/useRichTextTabs';
import { TabConfigDialog } from './TabConfigDialog';

export interface RichTextTabsManagerProps {
  entityType: EntityType;
  entityId: string;
  className?: string;
  defaultLanguage?: LanguageCode;
}

// 支持的语言配置
const SUPPORTED_LANGUAGES: { code: LanguageCode; name: string; flag: string }[] = [
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
];

export function RichTextTabsManager({
  entityType,
  entityId,
  className = '',
  defaultLanguage = 'zh',
}: RichTextTabsManagerProps) {
  const [selectedLanguage, setSelectedLanguage] = useState<LanguageCode>(defaultLanguage);

  // 配置管理状态
  const [configDialogOpen, setConfigDialogOpen] = useState(false);
  const [editingConfig, setEditingConfig] = useState<TabConfig | null>(null);
  const [showConfigManagement, setShowConfigManagement] = useState(false);
  
  // 使用富文本标签页 Hook
  const {
    configs,
    tabs,
    isLoading,
    error,
    currentLanguage,
    activeTabKey,
    getTabContent,
    updateTabContent,
    saveTab,
    saveAllTabs,
    isSaving,
    setActiveTab,
    hasUnsavedChanges,
    getUnsavedTabs,
    // 配置管理功能
    createConfig,
    updateConfig,
    deleteConfig,
    reorderConfigs,
    isConfigMutating,
  } = useRichTextTabs({
    entityType,
    entityId,
    languageCode: selectedLanguage,
    isAdminMode: true, // 管理模式，获取所有配置
  });
  
  // 处理语言切换
  const handleLanguageChange = (language: LanguageCode) => {
    if (hasUnsavedChanges) {
      const confirmed = window.confirm('您有未保存的更改，切换语言将丢失这些更改。是否继续？');
      if (!confirmed) return;
    }
    setSelectedLanguage(language);
  };
  
  // 处理内容变更
  const handleContentChange = (content: string) => {
    if (activeTabKey) {
      updateTabContent(activeTabKey, content);
    }
  };

  // 处理单个保存
  const handleSaveTab = async () => {
    if (!activeTabKey) return;

    try {
      await saveTab(activeTabKey);
    } catch (error) {
      console.error('保存失败:', error);
    }
  };

  // 处理批量保存
  const handleSaveAll = async () => {
    try {
      await saveAllTabs();
    } catch (error) {
      console.error('批量保存失败:', error);
    }
  };

  // 配置管理处理函数
  const handleCreateConfig = () => {
    setEditingConfig(null);
    setConfigDialogOpen(true);
  };

  const handleEditConfig = (config: TabConfig) => {
    setEditingConfig(config);
    setConfigDialogOpen(true);
  };

  const handleDeleteConfig = async (config: TabConfig) => {
    if (config.is_preset) {
      alert('预设标签页不能删除');
      return;
    }

    const confirmed = window.confirm(`确定要删除标签页 "${config.label}" 吗？`);
    if (!confirmed) return;

    try {
      await deleteConfig(config.id);
    } catch (error) {
      console.error('删除失败:', error);
    }
  };

  const handleSaveConfig = async (configData: any) => {
    try {
      if (editingConfig) {
        await updateConfig(editingConfig.id, configData);
      } else {
        await createConfig(configData);
      }
    } catch (error) {
      console.error('保存配置失败:', error);
      throw error;
    }
  };
  
  // 如果正在加载
  if (isLoading) {
    return (
      <RadixCard className={className}>
        <RadixCardContent className="flex items-center justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin mr-2" />
          <span>加载富文本标签页...</span>
        </RadixCardContent>
      </RadixCard>
    );
  }
  
  // 如果有错误
  if (error) {
    return (
      <RadixCard className={className}>
        <RadixCardContent className="py-6">
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              加载富文本标签页失败: {error.message || '未知错误'}
            </AlertDescription>
          </Alert>
        </RadixCardContent>
      </RadixCard>
    );
  }
  
  // 如果没有配置
  if (configs.length === 0) {
    return (
      <RadixCard className={className}>
        <RadixCardContent className="py-6">
          <Alert>
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              当前语言 ({currentLanguage}) 暂无可用的标签页配置
            </AlertDescription>
          </Alert>
        </RadixCardContent>
      </RadixCard>
    );
  }
  
  const unsavedTabs = getUnsavedTabs();
  
  return (
    <RadixCard className={cn("admin-form", className)}>
      <RadixCardHeader>
        <div className="flex items-center justify-between">
          <div>
            <RadixCardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5" />
              富文本标签页管理 (新版本)
            </RadixCardTitle>
            <RadixCardDescription>
              动态标签页配置，支持多语言内容管理
            </RadixCardDescription>
          </div>

          {/* 操作按钮组 */}
          <div className="flex items-center gap-2">
            {/* 配置管理按钮 */}
            <Button
              variant="outline"
              size="sm"
              onClick={() => setShowConfigManagement(!showConfigManagement)}
              className="flex items-center gap-2"
            >
              <Settings className="h-4 w-4" />
              {showConfigManagement ? '隐藏管理' : '配置管理'}
            </Button>

            {/* 语言切换器 */}
            <div className="flex items-center gap-2">
            <Globe className="h-4 w-4 text-muted-foreground" />
            <div className="flex gap-1">
              {SUPPORTED_LANGUAGES.map((lang) => (
                <button
                  key={lang.code}
                  onClick={() => handleLanguageChange(lang.code)}
                  className={cn(
                    "h-8 px-2 text-sm font-medium rounded-md transition-colors flex items-center gap-1",
                    selectedLanguage === lang.code
                      ? "bg-slate-900 text-white dark:bg-slate-100 dark:text-slate-900"
                      : "border border-slate-300 bg-white hover:bg-slate-50 dark:border-slate-600 dark:bg-slate-800 dark:hover:bg-slate-700"
                  )}
                >
                  <span>{lang.flag}</span>
                  {lang.name}
                </button>
              ))}
            </div>
          </div>
          </div>
        </div>
        
        {/* 未保存更改提示 */}
        {hasUnsavedChanges && (
          <Alert className="mt-4">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              您有未保存的更改在: {unsavedTabs.join(', ')}
            </AlertDescription>
          </Alert>
        )}
      </RadixCardHeader>
      
      <RadixCardContent>
        <Tabs.Root
          value={activeTabKey || undefined}
          onValueChange={setActiveTab}
          className="w-full"
        >
          {/* 标签页导航 */}
          <div className="space-y-4">
            <div className="flex items-center justify-between">
              <Tabs.List className="flex gap-1 p-1 bg-muted rounded-lg">
                {(configs || []).map((config) => {
                  const hasUnsaved = unsavedTabs.includes(config.key);

                  return (
                    <Tabs.Trigger
                      key={config.key}
                      value={config.key}
                      className={cn(
                        "flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-md",
                        "data-[state=active]:bg-background data-[state=active]:shadow-sm",
                        "hover:bg-background/50 transition-colors",
                        hasUnsaved && "text-orange-600"
                      )}
                    >
                      <span>{String(config.label)}</span>
                      {Boolean(config.is_preset) && (
                        <Badge variant="secondary" className="text-xs">
                          预设
                        </Badge>
                      )}
                      {hasUnsaved && (
                        <div className="w-2 h-2 bg-orange-500 rounded-full" />
                      )}
                    </Tabs.Trigger>
                  );
                })}
              </Tabs.List>

              {/* 添加新标签页按钮 */}
              {showConfigManagement && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleCreateConfig}
                  className="flex items-center gap-2"
                >
                  <Plus className="h-4 w-4" />
                  新建标签页
                </Button>
              )}
            </div>

            {/* 配置管理面板 */}
            {showConfigManagement && (
              <div className="border rounded-lg p-4 bg-muted/50">
                <h4 className="text-sm font-medium mb-3">标签页配置管理</h4>
                <div className="space-y-2">
                  {(configs || []).map((config) => (
                    <div
                      key={config.id}
                      className="flex items-center justify-between p-2 bg-background rounded border"
                    >
                      <div className="flex items-center gap-2">
                        <span className="font-medium">{config.label}</span>
                        <span className="text-xs text-muted-foreground">({config.key})</span>
                        {Boolean(config.is_preset) && (
                          <Badge variant="secondary" className="text-xs">
                            预设
                          </Badge>
                        )}
                        {!config.is_active && (
                          <Badge variant="outline" className="text-xs">
                            已禁用
                          </Badge>
                        )}
                      </div>
                      <div className="flex items-center gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          onClick={() => handleEditConfig(config)}
                          className="h-8 w-8 p-0"
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                        {!config.is_preset && (
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => handleDeleteConfig(config)}
                            className="h-8 w-8 p-0 text-red-600 hover:text-red-700"
                          >
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          {/* 标签页内容 */}
          {(configs || []).map((config) => (
            <Tabs.Content
              key={config.key}
              value={config.key}
              className="space-y-4 mt-0 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
            >
              {/* 富文本编辑器 */}
              <div className="border rounded-lg">
                <RichTextEditor
                  content={getTabContent(config.key)}
                  onChange={handleContentChange}
                  placeholder={config.placeholder || `请输入 ${config.label} 内容...`}
                  className="min-h-[300px]"
                />
              </div>
              
              {/* 操作按钮 */}
              <div className="flex items-center justify-between pt-4 border-t">
                <div className="text-sm text-muted-foreground">
                  语言: {currentLanguage.toUpperCase()} | 标签页: {config.label}
                </div>
                
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSaveTab}
                    disabled={isSaving || !unsavedTabs.includes(config.key)}
                  >
                    {isSaving ? (
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                    ) : (
                      <Save className="h-4 w-4 mr-2" />
                    )}
                    保存此标签页
                  </Button>
                  
                  {hasUnsavedChanges && (
                    <Button
                      size="sm"
                      onClick={handleSaveAll}
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      ) : (
                        <Save className="h-4 w-4 mr-2" />
                      )}
                      保存所有更改
                    </Button>
                  )}
                </div>
              </div>
            </Tabs.Content>
          ))}
        </Tabs.Root>
      </RadixCardContent>

      {/* 配置对话框 */}
      <TabConfigDialog
        open={configDialogOpen}
        onOpenChange={setConfigDialogOpen}
        config={editingConfig}
        entityType={entityType}
        languageCode={selectedLanguage}
        onSave={handleSaveConfig}
        isLoading={isConfigMutating}
      />
    </RadixCard>
  );
}
