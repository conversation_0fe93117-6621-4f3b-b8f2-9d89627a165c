# Ayafeed 设计指南

> 基于射命丸文主题的专业现代设计系统

## 📋 目录

- [设计理念](#设计理念)
- [色彩系统](#色彩系统)
- [视觉层次](#视觉层次)
- [组件规范](#组件规范)
- [用户引导](#用户引导)
- [可访问性](#可访问性)
- [实施计划](#实施计划)

## 🎨 设计理念

### 核心价值观

**专业新闻媒体感**
- 体现射命丸文作为天狗记者的专业性
- 现代化的信息展示和组织方式
- 快速、准确、可信的用户体验

**现代化设计语言**
- 遵循当代 Web 设计最佳实践
- 响应式设计，适配所有设备
- 性能优先，注重用户体验

**品牌一致性**
- 融入东方 Project 文化元素
- 保持 Ayafeed 平台特色
- 建立可识别的视觉身份

### 设计原则

1. **清晰性优先** - 信息层次分明，内容易于理解
2. **效率导向** - 减少用户认知负担，提升操作效率
3. **一致性** - 统一的视觉语言和交互模式
4. **可访问性** - 支持所有用户群体的无障碍访问

## 🌈 色彩系统

### 主色调方案

```css
/* 射命丸文新闻主题色彩 */
:root {
  /* 主色 - 新闻红 */
  --primary: oklch(0.55 0.22 15);           /* #DC2626 专业新闻红 */
  --primary-hover: oklch(0.50 0.24 15);     /* #B91C1C 悬停状态 */
  --primary-foreground: oklch(0.98 0 0);    /* #FEFEFE 白色文字 */
  
  /* 辅助色 - 天狗黑 */
  --secondary: oklch(0.15 0 0);             /* #262626 深黑色 */
  --secondary-hover: oklch(0.20 0 0);       /* #404040 悬停状态 */
  --secondary-foreground: oklch(0.98 0 0);  /* #FEFEFE 白色文字 */
  
  /* 强调色 - 金色 */
  --accent: oklch(0.75 0.15 85);            /* #F59E0B 金色强调 */
  --accent-hover: oklch(0.70 0.17 85);      /* #D97706 悬停状态 */
  --accent-foreground: oklch(0.15 0 0);     /* #262626 黑色文字 */
  
  /* 中性色 */
  --muted: oklch(0.96 0.01 15);             /* #F8F8F8 浅灰背景 */
  --muted-foreground: oklch(0.45 0.02 15);  /* #737373 中等灰色文字 */
  
  /* 语义色 */
  --success: oklch(0.55 0.15 145);          /* #16A34A 成功绿 */
  --warning: oklch(0.70 0.15 85);           /* #F59E0B 警告橙 */
  --error: oklch(0.55 0.22 15);             /* #DC2626 错误红 */
  --info: oklch(0.55 0.15 250);             /* #3B82F6 信息蓝 */
}

/* 深色模式 */
.dark {
  --primary: oklch(0.60 0.20 15);           /* 稍亮的红色 */
  --secondary: oklch(0.85 0 0);             /* 浅灰色 */
  --accent: oklch(0.80 0.12 85);            /* 稍亮的金色 */
  --background: oklch(0.08 0 0);            /* 深黑背景 */
  --foreground: oklch(0.95 0 0);            /* 浅色文字 */
}
```

### 渐变系统

```css
/* 背景渐变 */
--bg-gradient-light: linear-gradient(135deg, 
  oklch(0.99 0 0) 0%,           /* 纯白 */
  oklch(0.97 0.01 15) 50%,      /* 微红白 */
  oklch(0.95 0.02 15) 100%      /* 浅红白 */
);

--bg-gradient-dark: linear-gradient(135deg,
  oklch(0.08 0 0) 0%,           /* 深黑 */
  oklch(0.12 0.02 15) 50%,      /* 微红黑 */
  oklch(0.15 0.03 15) 100%      /* 浅红黑 */
);

/* 装饰渐变 */
--accent-gradient: linear-gradient(90deg,
  var(--primary) 0%,
  var(--accent) 100%
);
```

### 色彩使用指南

**主色使用场景：**
- 主要 CTA 按钮
- 重要链接和导航
- 品牌标识元素
- 紧急或重要信息标识

**辅助色使用场景：**
- 次要按钮
- 文字内容
- 边框和分割线
- 图标元素

**强调色使用场景：**
- 特殊标签和徽章
- 悬停状态
- 活跃状态指示
- 重点信息高亮

## 📐 视觉层次

### 字体系统

```css
/* 字体大小系统 */
:root {
  /* 标题层次 */
  --text-h1: clamp(2.5rem, 5vw, 4rem);     /* 40-64px */
  --text-h2: clamp(2rem, 4vw, 3rem);       /* 32-48px */
  --text-h3: clamp(1.5rem, 3vw, 2rem);     /* 24-32px */
  --text-h4: clamp(1.25rem, 2.5vw, 1.5rem); /* 20-24px */
  
  /* 正文层次 */
  --text-lg: 1.125rem;                     /* 18px */
  --text-base: 1rem;                       /* 16px */
  --text-sm: 0.875rem;                     /* 14px */
  --text-xs: 0.75rem;                      /* 12px */
  
  /* 行高 */
  --leading-tight: 1.25;
  --leading-normal: 1.5;
  --leading-relaxed: 1.75;
}
```

### 间距系统

```css
/* 间距系统 - 基于 8px 网格 */
:root {
  --space-0: 0;
  --space-1: 0.25rem;    /* 4px */
  --space-2: 0.5rem;     /* 8px */
  --space-3: 0.75rem;    /* 12px */
  --space-4: 1rem;       /* 16px */
  --space-5: 1.25rem;    /* 20px */
  --space-6: 1.5rem;     /* 24px */
  --space-8: 2rem;       /* 32px */
  --space-10: 2.5rem;    /* 40px */
  --space-12: 3rem;      /* 48px */
  --space-16: 4rem;      /* 64px */
  --space-20: 5rem;      /* 80px */
  --space-24: 6rem;      /* 96px */
}
```

### 阴影系统

```css
/* 阴影层次 */
:root {
  --shadow-sm: 0 1px 2px oklch(0 0 0 / 0.05);
  --shadow-md: 0 4px 6px oklch(0 0 0 / 0.07), 0 2px 4px oklch(0 0 0 / 0.06);
  --shadow-lg: 0 10px 15px oklch(0 0 0 / 0.1), 0 4px 6px oklch(0 0 0 / 0.05);
  --shadow-xl: 0 20px 25px oklch(0 0 0 / 0.1), 0 10px 10px oklch(0 0 0 / 0.04);
  
  /* 特殊阴影 */
  --shadow-news: 0 4px 12px oklch(0.55 0.22 15 / 0.15);  /* 新闻红阴影 */
  --shadow-accent: 0 4px 12px oklch(0.75 0.15 85 / 0.15); /* 金色阴影 */
}
```

## 🧩 组件规范

### 按钮组件

```typescript
// 按钮变体定义
const buttonVariants = {
  // 主要按钮 - 用于主要操作
  primary: "bg-primary text-primary-foreground hover:bg-primary-hover shadow-md hover:shadow-lg",
  
  // 次要按钮 - 用于次要操作
  secondary: "bg-secondary text-secondary-foreground hover:bg-secondary-hover",
  
  // 轮廓按钮 - 用于可选操作
  outline: "border border-primary text-primary hover:bg-primary hover:text-primary-foreground",
  
  // 幽灵按钮 - 用于低优先级操作
  ghost: "text-primary hover:bg-primary/10",
  
  // 危险按钮 - 用于删除等危险操作
  destructive: "bg-error text-white hover:bg-error/90"
};

// 按钮尺寸定义
const buttonSizes = {
  sm: "h-8 px-3 text-sm",
  md: "h-10 px-4",
  lg: "h-12 px-6 text-lg",
  icon: "h-10 w-10"
};
```

### 卡片组件

```typescript
// 新闻卡片组件规范
interface NewsCardProps {
  title: string;
  excerpt: string;
  publishTime: string;
  category: string;
  urgent?: boolean;
  featured?: boolean;
  imageUrl?: string;
}

// 卡片样式变体
const cardVariants = {
  default: "bg-card border border-border rounded-xl shadow-sm hover:shadow-md transition-all",
  featured: "bg-gradient-to-br from-primary/5 to-accent/5 border-primary/20",
  urgent: "border-l-4 border-l-primary bg-primary/5"
};
```

### 导航组件

```typescript
// 导航项配置
const navigationItems = [
  { 
    href: "/", 
    label: "首页", 
    icon: "Home", 
    description: "最新展会资讯",
    shortcut: "H"
  },
  { 
    href: "/events", 
    label: "展会", 
    icon: "Calendar", 
    description: "展会信息中心",
    shortcut: "E"
  },
  { 
    href: "/circles", 
    label: "社团", 
    icon: "Users", 
    description: "社团数据库",
    shortcut: "C"
  },
  { 
    href: "/feed", 
    label: "动态", 
    icon: "Activity", 
    description: "实时更新",
    shortcut: "F",
    badge: "即将推出"
  },
] as const;

// 后续迭代项目
const futureNavigationItems = [
  { 
    href: "/news", 
    label: "新闻", 
    icon: "Newspaper", 
    description: "行业动态",
    shortcut: "N",
    status: "planned"
  },
] as const;
```

## 🧭 用户引导

### 信息架构原则

1. **渐进式披露** - 按需展示信息，避免信息过载
2. **清晰的视觉层次** - 使用大小、颜色、间距建立层次
3. **一致的导航模式** - 统一的导航位置和交互方式
4. **有意义的反馈** - 及时的状态反馈和错误提示

### 导航设计规范

```typescript
// 面包屑导航
export function Breadcrumb() {
  return (
    <nav className="flex items-center space-x-2 text-sm text-muted-foreground mb-6">
      <Home className="h-4 w-4" />
      <ChevronRight className="h-4 w-4" />
      <span className="text-foreground font-medium">当前页面</span>
    </nav>
  );
}

// 页面标题区域
export function PageHeader({ title, description, actions }: PageHeaderProps) {
  return (
    <div className="flex items-center justify-between mb-8">
      <div>
        <h1 className="text-h1 font-bold text-foreground">{title}</h1>
        {description && (
          <p className="text-lg text-muted-foreground mt-2">{description}</p>
        )}
      </div>
      {actions && <div className="flex gap-3">{actions}</div>}
    </div>
  );
}
```

### 状态反馈系统

```typescript
// 加载状态
export function LoadingState() {
  return (
    <div className="flex items-center justify-center py-12">
      <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      <span className="ml-3 text-muted-foreground">加载中...</span>
    </div>
  );
}

// 空状态
export function EmptyState({ title, description, action }: EmptyStateProps) {
  return (
    <div className="text-center py-12">
      <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted flex items-center justify-center">
        <Search className="h-8 w-8 text-muted-foreground" />
      </div>
      <h3 className="text-lg font-medium text-foreground mb-2">{title}</h3>
      <p className="text-muted-foreground mb-6">{description}</p>
      {action}
    </div>
  );
}
```

## ♿ 可访问性

### 键盘导航

```typescript
// 键盘快捷键支持
const keyboardShortcuts = {
  'h': () => router.push('/'),           // 首页
  'e': () => router.push('/events'),     // 展会
  'c': () => router.push('/circles'),    // 社团
  'f': () => router.push('/feed'),       // 动态
  '/': () => focusSearch(),              // 搜索
  'Escape': () => closeModal(),          // 关闭弹窗
} as const;
```

### 语义化 HTML

```html
<!-- 正确的语义化结构 -->
<main role="main">
  <header>
    <h1>页面标题</h1>
    <nav aria-label="面包屑导航">
      <!-- 面包屑内容 -->
    </nav>
  </header>
  
  <section aria-labelledby="events-heading">
    <h2 id="events-heading">展会列表</h2>
    <!-- 展会内容 -->
  </section>
</main>
```

### ARIA 标签规范

```typescript
// 可访问性属性
const accessibilityProps = {
  // 按钮
  button: {
    'aria-label': '描述性标签',
    'aria-describedby': '额外描述ID',
    'aria-pressed': 'true/false', // 切换按钮
  },
  
  // 输入框
  input: {
    'aria-label': '输入框用途',
    'aria-required': 'true/false',
    'aria-invalid': 'true/false',
    'aria-describedby': '错误信息ID',
  },
  
  // 导航
  nav: {
    'aria-label': '导航类型',
    'aria-current': 'page', // 当前页面
  },
} as const;
```

## 📅 实施计划

### 第一阶段：基础设计系统（1-2 周）

**优先级：高**

- [ ] 更新色彩系统变量
- [ ] 实施新的按钮组件
- [ ] 优化导航栏设计
- [ ] 添加基础装饰元素

**交付物：**
- 更新的 CSS 变量文件
- 重构的按钮组件
- 新的导航栏组件
- 基础设计 token

### 第二阶段：组件优化（2-3 周）

**优先级：中**

- [ ] 实施新闻卡片组件
- [ ] 添加面包屑导航
- [ ] 优化视觉层次
- [ ] 完善状态反馈

**交付物：**
- 完整的卡片组件库
- 导航组件系统
- 状态管理组件
- 视觉层次规范

### 第三阶段：高级功能（3-4 周）

**优先级：中低**

- [ ] 完善可访问性功能
- [ ] 添加高级动画效果
- [ ] 性能监控和优化
- [ ] 响应式设计完善

**交付物：**
- 可访问性测试报告
- 动画效果库
- 性能优化方案
- 响应式设计指南

### 后续迭代计划

**新功能模块：**
- [ ] `/news` 新闻模块设计
- [ ] `/feed` 动态流设计
- [ ] 高级搜索界面
- [ ] 个性化推荐系统

**设计系统扩展：**
- [ ] 深色模式完善
- [ ] 多主题支持
- [ ] 国际化设计适配
- [ ] 移动端专属组件

---

## 📚 参考资源

- [Material Design 3](https://m3.material.io/)
- [Apple Human Interface Guidelines](https://developer.apple.com/design/human-interface-guidelines/)
- [Web Content Accessibility Guidelines (WCAG) 2.1](https://www.w3.org/WAI/WCAG21/quickref/)
- [Radix UI Design System](https://www.radix-ui.com/primitives)

---

*本文档将随着项目发展持续更新，确保设计系统的一致性和现代性。*
