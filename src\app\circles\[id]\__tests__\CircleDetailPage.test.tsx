// @ts-nocheck
import { expect, test, vi, beforeEach } from "vitest";

import CircleDetailPage from "@/app/circles/[id]/page";
import { renderWithProviders, screen } from "@test/test-utils";
import { server, http } from "@test/testServer";

// Mock useParams 返回固定 id
vi.mock("next/navigation", () => {
  return { useParams: () => ({ id: "c1" }) };
});

// Mock react-virtuoso VirtuosoGrid => 渲染静态列表
vi.mock("react-virtuoso", () => {
  return {
    __esModule: true,
    VirtuosoGrid: ({ data, itemContent }: any) => (
      <div data-testid="virtuoso-grid">
        {data.map((item: any, idx: number) => itemContent(idx, item))}
      </div>
    ),
  };
});

// Mock next/image
vi.mock("next/image", () => ({
  default: (props: any) => {
    // eslint-disable-next-line jsx-a11y/alt-text, @next/next/no-img-element
    return <img {...props} />;
  },
}));

const sampleCircle = {
  id: "c1",
  name: "幻想郷楽団",
  urls: "{}",
};

const sampleAppearances = [
  {
    id: "a1",
    name: "例大祭 21",
    date: "2025-05-04",
    booth_id: "あ01a",
    event_id: "evt1",
    venue_name: "東京ビッグサイト",
  },
];

// Mock hooks
vi.mock("@/api/generated/ayafeedComponents", () => {
  return {
    __esModule: true,
    useGetCirclesId: () => ({ data: sampleCircle, isLoading: false }),
    useGetAppearances: () => ({ data: sampleAppearances, isLoading: false }),
    skipToken: {},
  };
});

beforeEach(() => {
  server.resetHandlers();
  server.use(
    http.get("http://127.0.0.1:8787/circles/c1", (_req, res, ctx) => {
      return res(ctx.status(200), ctx.json(sampleCircle));
    }),
    http.get("http://127.0.0.1:8787/appearances", (_req, res, ctx) => {
      return res(ctx.status(200), ctx.json({ items: sampleAppearances }));
    })
  );
});

test("renders circle detail page with appearances list", async () => {
  renderWithProviders(<CircleDetailPage />);

  // 基本信息
  expect(await screen.findByRole("heading", { name: /幻想郷楽団/ })).toBeInTheDocument();

  // Booth 信息
  expect(await screen.findByText("あ01a")).toBeInTheDocument();
}); 