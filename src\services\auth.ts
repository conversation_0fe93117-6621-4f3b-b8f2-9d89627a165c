/**
 * 认证服务
 * 基于 Better Auth 官方客户端的认证集成
 */

import { authClient } from '@/lib/auth-client';
import type { User } from '@/types/user';

export interface LoginCredentials {
  email: string;
  password: string;
}

export interface UsernameLoginCredentials {
  username: string;
  password: string;
}

export interface RegisterData {
  email: string;
  password: string;
  name?: string;
  username?: string;
}

export interface Session {
  id: string;
  userId: string;
  expiresAt: string;
}

export interface AuthResponse {
  user: User;
  session: Session;
}

export class AuthService {
  private static USER_KEY = 'auth_user';
  private static currentUserPromise: Promise<User> | null = null;
  private static authCheckPromise: Promise<boolean> | null = null;

  // 添加缓存机制，减少频繁请求
  private static userCache: { user: User; timestamp: number } | null = null;
  private static CACHE_DURATION = 30 * 1000; // 30秒缓存

  // 邮箱登录
  static async login(credentials: LoginCredentials): Promise<AuthResponse> {
    const { data, error } = await authClient.signIn.email({
      email: credentials.email,
      password: credentials.password,
    });

    console.debug('[AuthService] Better Auth 登录响应:', { data, error });

    // 检查响应是否有效
    if (error || !data) {
      console.warn('[AuthService] 登录失败:', error);
      throw new Error(error?.message || '登录失败，请检查用户名和密码');
    }

    // Better Auth 返回的用户数据（后端已包含 role 字段）
    const betterAuthUser = data.user;

    console.debug('[AuthService] Better Auth 用户数据:', betterAuthUser);

    // 直接使用后端返回的用户数据（包含 role 字段）
    const user: User = {
      id: betterAuthUser.id,
      email: betterAuthUser.email,
      name: betterAuthUser.name,
      role: (betterAuthUser as any).role || 'user', // 后端返回 role 字段
      emailVerified: betterAuthUser.emailVerified,
      createdAt: betterAuthUser.createdAt.toISOString(),
      updatedAt: betterAuthUser.updatedAt.toISOString(),
    };

    console.debug('[AuthService] 用户信息:', user);

    // 只存储用户信息，认证依赖 Cookie
    this.setUser(user);

    // 更新缓存
    this.userCache = {
      user,
      timestamp: Date.now()
    };

    // 创建 session 对象（Better Auth 的 session 在 cookie 中）
    const session: Session = {
      id: data.token || 'cookie-session',
      userId: user.id,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(), // 30天
    };

    return { user, session };
  }

  // 用户名登录
  static async loginWithUsername(credentials: UsernameLoginCredentials): Promise<AuthResponse> {
    // 使用 Better Auth 客户端的用户名登录（后端支持）
    const { data, error } = await authClient.signIn.username({
      username: credentials.username,
      password: credentials.password,
    });

    console.debug('[AuthService] Better Auth 用户名登录响应:', { data, error });

    // 检查响应是否有效
    if (error || !data) {
      console.warn('[AuthService] 用户名登录失败:', error);
      throw new Error(error?.message || '登录失败，请检查用户名和密码');
    }

    // Better Auth 返回的用户数据（后端已包含 role 字段）
    const betterAuthUser = data.user;

    // 直接使用后端返回的用户数据（包含 role 字段）
    const user: User = {
      id: betterAuthUser.id,
      email: betterAuthUser.email,
      name: betterAuthUser.name,
      role: (betterAuthUser as any).role || 'user', // 后端返回 role 字段
      emailVerified: betterAuthUser.emailVerified,
      createdAt: betterAuthUser.createdAt.toISOString(),
      updatedAt: betterAuthUser.updatedAt.toISOString(),
    };

    console.debug('[AuthService] 用户名登录用户信息:', user);

    // 只存储用户信息，认证依赖 Cookie
    this.setUser(user);

    // 更新缓存
    this.userCache = {
      user,
      timestamp: Date.now()
    };

    // 创建 session 对象
    const session: Session = {
      id: data.token || 'cookie-session',
      userId: user.id,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    };

    return { user, session };
  }

  // 智能登录 - 自动检测邮箱或用户名
  static async smartLogin(identifier: string, password: string): Promise<AuthResponse> {
    // 检测是否为邮箱格式
    const isEmail = identifier.includes('@');

    if (isEmail) {
      return this.login({ email: identifier, password });
    } else {
      return this.loginWithUsername({ username: identifier, password });
    }
  }

  // 用户注册
  static async register(userData: RegisterData): Promise<AuthResponse> {
    const { data, error } = await authClient.signUp.email({
      email: userData.email,
      password: userData.password,
      name: userData.name || userData.email.split('@')[0], // 如果没有 name，使用邮箱前缀
      username: userData.username, // 支持用户名注册
    });

    console.debug('[AuthService] Better Auth 注册响应:', { data, error });

    // 检查响应是否有效
    if (error || !data) {
      console.warn('[AuthService] 注册失败:', error);
      throw new Error(error?.message || '注册失败，请稍后重试');
    }

    // Better Auth 返回的用户数据（后端已包含 role 字段）
    const betterAuthUser = data.user;

    // 直接使用后端返回的用户数据（包含 role 字段）
    const user: User = {
      id: betterAuthUser.id,
      email: betterAuthUser.email,
      name: betterAuthUser.name,
      role: (betterAuthUser as any).role || 'user', // 后端返回 role 字段
      emailVerified: betterAuthUser.emailVerified,
      createdAt: betterAuthUser.createdAt.toISOString(),
      updatedAt: betterAuthUser.updatedAt.toISOString(),
    };

    // 存储用户信息
    this.setUser(user);

    // 更新缓存
    this.userCache = {
      user,
      timestamp: Date.now()
    };

    // 创建 session 对象
    const session: Session = {
      id: data.token || 'cookie-session',
      userId: user.id,
      expiresAt: new Date(Date.now() + 30 * 24 * 60 * 60 * 1000).toISOString(),
    };

    return { user, session };
  }

  // 退出登录
  static async logout(): Promise<void> {
    try {
      // 使用 Better Auth 客户端登出
      const response = await authClient.signOut();

      console.debug('[AuthService] Better Auth 登出响应:', response);
    } catch (error: any) {
      console.warn('[AuthService] Better Auth 登出失败:', error);
      // 即使服务端登出失败，也要清除本地状态
    } finally {
      // 无论服务端登出是否成功，都要清除本地状态
      this.clearAuth();

      console.debug('[AuthService] 本地认证数据已清除');
    }
  }

  // 获取当前用户信息（带去重和缓存）
  static async getCurrentUser(silent: boolean = false): Promise<User> {
    // 检查缓存是否有效
    if (this.userCache && Date.now() - this.userCache.timestamp < this.CACHE_DURATION) {
      console.debug('[AuthService] 使用缓存的用户信息');
      return this.userCache.user;
    }

    // 如果已有进行中的请求，返回同一个Promise
    if (this.currentUserPromise) {
      console.debug('[AuthService] 复用进行中的用户请求');
      return this.currentUserPromise;
    }

    console.debug('[AuthService] 发起新的用户信息请求');
    this.currentUserPromise = this._fetchCurrentUser(silent);

    try {
      const user = await this.currentUserPromise;
      // 更新缓存
      this.userCache = {
        user,
        timestamp: Date.now()
      };
      return user;
    } finally {
      // 请求完成后清除Promise，允许下次请求
      this.currentUserPromise = null;
    }
  }

  private static async _fetchCurrentUser(silent: boolean = false): Promise<User> {
    // 使用 Better Auth 客户端获取 session
    const { data, error } = await authClient.getSession();

    console.debug('[AuthService] Better Auth session:', { data, error });

    // 检查 session 是否有效
    if (error || !data || !data.user) {
      if (!silent) {
        console.warn('[AuthService] 用户未登录或会话已过期:', error);
      }
      throw new Error('用户未登录或会话已过期');
    }

    // Better Auth 返回的用户数据（后端已包含 role 字段）
    const betterAuthUser = data.user;

    console.debug('[AuthService] Better Auth 用户数据:', betterAuthUser);

    // 直接使用后端返回的用户数据（包含 role 字段）
    const user: User = {
      id: betterAuthUser.id,
      email: betterAuthUser.email,
      name: betterAuthUser.name,
      role: (betterAuthUser as any).role || 'user', // 后端返回 role 字段
      emailVerified: betterAuthUser.emailVerified,
      createdAt: betterAuthUser.createdAt.toISOString(),
      updatedAt: betterAuthUser.updatedAt.toISOString(),
    };

    console.debug('[AuthService] 用户信息:', user);

    // 更新本地用户信息
    this.setUser(user);
    return user;
  }

  // 认证状态检查（基于 Cookie，带去重）
  static async isAuthenticated(silent: boolean = false): Promise<boolean> {
    // 如果已有进行中的认证检查，返回同一个Promise
    if (this.authCheckPromise) {
      return this.authCheckPromise;
    }

    this.authCheckPromise = this._checkAuthentication(silent);

    try {
      const isAuth = await this.authCheckPromise;
      return isAuth;
    } finally {
      // 请求完成后清除Promise，允许下次请求
      this.authCheckPromise = null;
    }
  }

  private static async _checkAuthentication(silent: boolean = false): Promise<boolean> {
    try {
      await this.getCurrentUser(silent);
      return true;
    } catch {
      // 如果获取用户信息失败，清除本地缓存
      this.clearUser();
      return false;
    }
  }

  // 用户信息管理
  static setUser(user: User): void {
    if (typeof window !== 'undefined') {
      localStorage.setItem(this.USER_KEY, JSON.stringify(user));
    }
  }

  static getUser(): User | null {
    if (typeof window !== 'undefined') {
      const userStr = localStorage.getItem(this.USER_KEY);
      if (userStr) {
        try {
          const user = JSON.parse(userStr);
          console.debug('[AuthService] 从 localStorage 获取用户:', user);
          console.debug('[AuthService] localStorage 用户的 role:', user.role);

          // 如果用户数据缺少 role 字段，清除缓存并返回 null
          if (user && !user.role) {
            console.warn('[AuthService] 用户数据缺少 role 字段，清除缓存');
            this.clearAuth();
            return null;
          }

          return user;
        } catch (error) {
          console.warn('Failed to parse user from localStorage:', error);
          // 清除损坏的数据
          localStorage.removeItem(this.USER_KEY);
          return null;
        }
      }
    }
    return null;
  }

  static clearUser(): void {
    if (typeof window !== 'undefined') {
      localStorage.removeItem(this.USER_KEY);
    }
  }

  // 清除所有认证信息
  static clearAuth(): void {
    this.clearUser();
    // 清除缓存
    this.userCache = null;
    // 清除进行中的请求
    this.currentUserPromise = null;
    this.authCheckPromise = null;
  }

  // 权限检查（基于实时用户信息）
  static async hasPermission(requiredRoles: string[]): Promise<boolean> {
    try {
      const user = await this.getCurrentUser();
      return requiredRoles.includes(user.role);
    } catch {
      return false;
    }
  }
}

export const authService = AuthService;
