#!/usr/bin/env tsx
/**
 * 代码生成后处理脚本
 * 
 * 在 openapi-codegen 生成代码后自动应用语言支持的修改
 * 确保重新生成代码时不会丢失语言切换功能
 */

import fs from 'fs';
import path from 'path';

const CONTEXT_FILE = path.join(process.cwd(), 'src/api/generated/ayafeedContext.ts');

/**
 * 检查文件是否存在
 */
function fileExists(filePath: string): boolean {
  try {
    return fs.statSync(filePath).isFile();
  } catch {
    return false;
  }
}

/**
 * 为 ayafeedContext.ts 添加语言支持
 */
function patchContextFile(): boolean {
  if (!fileExists(CONTEXT_FILE)) {
    console.error(`❌ Context file not found: ${CONTEXT_FILE}`);
    return false;
  }

  let content = fs.readFileSync(CONTEXT_FILE, 'utf8');
  let modified = false;

  // 1. 添加 getCurrentLocale 导入
  if (!content.includes('getCurrentLocale')) {
    const importRegex = /import { QueryOperation } from "\.\/ayafeedComponents";/;
    if (importRegex.test(content)) {
      content = content.replace(
        importRegex,
        `import { QueryOperation } from "./ayafeedComponents";
import { getCurrentLocale } from "@/lib/locale-utils";`
      );
      modified = true;
      console.log('✅ Added getCurrentLocale import');
    }
  }

  // 2. 修改 useAyafeedContext 函数
  const originalReturnPattern = /return\s*\{\s*fetcherOptions:\s*\{\},\s*queryOptions:\s*\{\},?\s*\};/s;
  
  if (originalReturnPattern.test(content) && !content.includes('locale: currentLocale')) {
    const newReturnStatement = `// 自动添加当前语言到查询参数，确保不同语言的数据有独立的缓存
  const currentLocale = getCurrentLocale();
  
  return {
    fetcherOptions: {
      queryParams: {
        locale: currentLocale,
      },
    },
    queryOptions: {},
  };`;

    content = content.replace(originalReturnPattern, newReturnStatement);
    modified = true;
    console.log('✅ Updated useAyafeedContext function');
  }

  // 3. 写回文件
  if (modified) {
    fs.writeFileSync(CONTEXT_FILE, content, 'utf8');
    console.log('✅ Context file patched successfully');
    return true;
  } else {
    console.log('ℹ️ Context file already contains locale support');
    return true;
  }
}

/**
 * 验证修改是否成功
 */
function verifyPatch(): boolean {
  if (!fileExists(CONTEXT_FILE)) {
    return false;
  }

  const content = fs.readFileSync(CONTEXT_FILE, 'utf8');

  // 检查是否有 getCurrentLocale 导入（即使当前没有使用）
  const hasImport = content.includes('getCurrentLocale');

  // 检查是否有合理的 useAyafeedContext 实现
  // 可以是带 locale 逻辑的，也可以是明确移除 locale 的（带注释说明）
  const hasLocaleLogic = content.includes('locale: currentLocale');
  const hasExplicitNoLocale = content.includes('不再全局添加 locale 查询参数') ||
                              content.includes('避免与请求体中的 language_code 冲突');

  if (hasImport && (hasLocaleLogic || hasExplicitNoLocale)) {
    console.log('✅ Verification passed: locale handling is properly configured');
    return true;
  } else {
    console.error('❌ Verification failed:');
    if (!hasImport) console.error('  - Missing getCurrentLocale import');
    if (!hasLocaleLogic && !hasExplicitNoLocale) {
      console.error('  - Missing locale logic or explicit no-locale configuration in useAyafeedContext');
    }
    return false;
  }
}

/**
 * 主函数
 */
function main() {
  console.log('🔧 Starting post-codegen processing...');
  
  try {
    const success = patchContextFile();
    
    if (success) {
      const verified = verifyPatch();
      if (verified) {
        console.log('🎉 Post-codegen processing completed successfully!');
        process.exit(0);
      } else {
        console.error('❌ Post-codegen processing failed verification');
        process.exit(1);
      }
    } else {
      console.error('❌ Post-codegen processing failed');
      process.exit(1);
    }
  } catch (error) {
    console.error('❌ Post-codegen processing error:', error);
    process.exit(1);
  }
}

// 运行主函数
if (require.main === module) {
  main();
}

export { patchContextFile, verifyPatch };
