/**
 * Ayafeed 设计系统组件库
 * 
 * 这个文件包含了基于设计系统的可复用组件
 * 所有组件都遵循 Ayafeed 的现代艺术风格设计原则
 */

import { motion } from 'motion/react'
import { ReactNode } from 'react'

// ============================================================================
// 布局组件
// ============================================================================

/**
 * 现代页面布局组件 - 射命丸文新闻主题
 * 提供标准的新闻主题背景和装饰元素
 */
interface ModernPageLayoutProps {
  children: ReactNode
  className?: string
}

function ModernPageLayout({ children, className = '' }: ModernPageLayoutProps) {
  return (
    <div className={`min-h-screen news-gradient-bg vintage-paper-texture relative overflow-hidden ${className}`}>
      {/* 射命丸文主题装饰元素 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        {/* 风的装饰线条 */}
        <div className="wind-decoration top-20 right-32"></div>
        <div className="wind-decoration-reverse top-40 left-48"></div>
        <div className="wind-decoration top-60 right-64"></div>
        <div className="wind-decoration-reverse top-80 left-24"></div>

        {/* 新闻主题渐变装饰 */}
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-primary/10 to-accent/10 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-accent/10 to-primary/10 rounded-full blur-3xl"></div>
        <div className="absolute top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 w-96 h-96 bg-gradient-to-br from-primary/5 to-accent/5 rounded-full blur-3xl"></div>
      </div>

      {/* 新闻纸纹理背景 */}
      <div className="absolute inset-0 newspaper-texture pointer-events-none"></div>

      <div className="relative">
        {children}
      </div>
    </div>
  )
}

/**
 * 容器组件
 * 提供标准的最大宽度和内边距
 */
interface ContainerProps {
  children: ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg' | 'xl' | 'full'
}

function Container({ children, className = '', size = 'xl' }: ContainerProps) {
  const sizeClasses = {
    sm: 'max-w-3xl',
    md: 'max-w-5xl', 
    lg: 'max-w-6xl',
    xl: 'max-w-7xl',
    full: 'max-w-full'
  }
  
  return (
    <div className={`${sizeClasses[size]} mx-auto px-4 md:px-6 lg:px-8 ${className}`}>
      {children}
    </div>
  )
}

// ============================================================================
// 卡片组件
// ============================================================================

/**
 * 现代卡片组件
 * 包含毛玻璃效果、渐变背景和悬停动画
 */
interface ModernCardProps {
  children: ReactNode
  className?: string
  hover?: boolean
  padding?: 'none' | 'sm' | 'md' | 'lg'
}

function ModernCard({ children, className = '', hover = true, padding = 'md' }: ModernCardProps) {
  const paddingClasses = {
    none: '',
    sm: 'p-4',
    md: 'p-6',
    lg: 'p-8'
  }

  return (
    <motion.div
      className={`overflow-hidden border border-border/50 bg-card/80 backdrop-blur-xl rounded-xl transition-all duration-300 relative group ${hover ? 'hover:shadow-2xl hover:shadow-primary/10 hover:border-primary/20' : ''} ${paddingClasses[padding]} ${className}`}
      whileHover={hover ? { y: -8, scale: 1.02 } : undefined}
      transition={{ duration: 0.3, ease: "easeOut" }}
    >
      {/* 新闻主题悬停效果 */}
      {hover && (
        <div className="absolute inset-0 bg-gradient-to-r from-primary/0 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-xl" />
      )}

      {/* 风的装饰效果 */}
      {hover && (
        <div className="absolute top-4 right-4 wind-decoration opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      )}

      <div className="relative">
        {children}
      </div>
    </motion.div>
  )
}

/**
 * 图片卡片组件
 * 专门用于展示图片内容的卡片
 */
interface ImageCardProps {
  children: ReactNode
  className?: string
  imageClassName?: string
}

function ImageCard({ children, className = '', imageClassName = '' }: ImageCardProps) {
  return (
    <ModernCard className={`group ${className}`} padding="none">
      <div className={`relative overflow-hidden ${imageClassName}`}>
        {children}
        {/* 悬停遮罩 */}
        <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
        {/* 新闻主题装饰性渐变边框 */}
        <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-accent/20 to-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300" style={{ mixBlendMode: 'overlay' }} />
        {/* 风的装饰效果 */}
        <div className="absolute top-4 left-4 wind-decoration opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
      </div>
    </ModernCard>
  )
}

// ============================================================================
// 文字组件
// ============================================================================

/**
 * 渐变标题组件
 * 提供统一的渐变文字效果
 */
interface GradientHeadingProps {
  children: ReactNode
  level?: 1 | 2 | 3 | 4 | 5 | 6
  className?: string
  gradient?: 'default' | 'brand' | 'accent'
}

function GradientHeading({ children, level = 1, className = '', gradient = 'default' }: GradientHeadingProps) {
  const Tag = `h${level}` as keyof JSX.IntrinsicElements

  const sizeClasses = {
    1: 'text-4xl md:text-5xl lg:text-6xl',
    2: 'text-3xl md:text-4xl',
    3: 'text-2xl md:text-3xl',
    4: 'text-xl md:text-2xl',
    5: 'text-lg md:text-xl',
    6: 'text-base md:text-lg'
  }

  const gradientClasses = {
    default: 'from-foreground to-foreground/70',
    brand: 'from-primary to-primary/70',
    accent: 'from-primary via-accent to-primary'
  }

  return (
    <Tag className={`font-bold bg-gradient-to-r ${gradientClasses[gradient]} bg-clip-text text-transparent ${sizeClasses[level]} ${className}`}>
      {children}
    </Tag>
  )
}

/**
 * 副标题组件
 */
interface SubtitleProps {
  children: ReactNode
  className?: string
  size?: 'sm' | 'md' | 'lg'
}

function Subtitle({ children, className = '', size = 'md' }: SubtitleProps) {
  const sizeClasses = {
    sm: 'text-sm',
    md: 'text-base md:text-lg',
    lg: 'text-lg md:text-xl'
  }

  return (
    <p className={`text-muted-foreground ${sizeClasses[size]} ${className}`}>
      {children}
    </p>
  )
}

// ============================================================================
// 表单组件
// ============================================================================

/**
 * 现代输入框组件 - 支持完整的可访问性功能
 */
interface ModernInputProps extends React.InputHTMLAttributes<HTMLInputElement> {
  className?: string
  label?: string
  error?: string
  helperText?: string
  leftIcon?: ReactNode
  rightIcon?: ReactNode
}

function ModernInput({
  className = '',
  label,
  error,
  helperText,
  leftIcon,
  rightIcon,
  id,
  ...props
}: ModernInputProps) {
  const inputId = id || `input-${Math.random().toString(36).substr(2, 9)}`
  const errorId = error ? `${inputId}-error` : undefined
  const helperId = helperText ? `${inputId}-helper` : undefined

  return (
    <div className="space-y-2">
      {label && (
        <label
          htmlFor={inputId}
          className="block text-sm font-medium text-foreground"
        >
          {label}
        </label>
      )}

      <div className="relative">
        {leftIcon && (
          <div className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
            {leftIcon}
          </div>
        )}

        <input
          id={inputId}
          className={`bg-background/60 dark:bg-card/60 backdrop-blur-sm border transition-all duration-300 rounded-xl py-2 w-full placeholder:text-muted-foreground focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2 ${
            error
              ? 'border-destructive focus:border-destructive'
              : 'border-border focus:bg-background/80 dark:focus:bg-card/80 focus:border-primary'
          } ${
            leftIcon ? 'pl-10' : 'pl-4'
          } ${
            rightIcon ? 'pr-10' : 'pr-4'
          } ${className}`}
          aria-invalid={error ? 'true' : 'false'}
          aria-describedby={[errorId, helperId].filter(Boolean).join(' ') || undefined}
          {...props}
        />

        {rightIcon && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">
            {rightIcon}
          </div>
        )}
      </div>

      {error && (
        <p id={errorId} className="text-sm text-destructive" role="alert">
          {error}
        </p>
      )}

      {helperText && !error && (
        <p id={helperId} className="text-sm text-muted-foreground">
          {helperText}
        </p>
      )}
    </div>
  )
}

/**
 * 现代按钮组件 - 射命丸文新闻主题，支持完整的可访问性功能
 */
interface ModernButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'primary' | 'secondary' | 'outline' | 'ghost' | 'accent' | 'destructive'
  size?: 'sm' | 'md' | 'lg'
  children: ReactNode
  loading?: boolean
  loadingText?: string
  icon?: ReactNode
  iconPosition?: 'left' | 'right'
}

function ModernButton({
  variant = 'primary',
  size = 'md',
  className = '',
  children,
  loading = false,
  loadingText = '加载中...',
  icon,
  iconPosition = 'left',
  disabled,
  ...props
}: ModernButtonProps) {
  const baseClasses = 'backdrop-blur-sm transition-all duration-300 rounded-xl font-medium focus:outline-none focus:ring-2 focus:ring-primary/20 focus:ring-offset-2 disabled:opacity-50 disabled:cursor-not-allowed relative overflow-hidden'

  const variantClasses = {
    primary: 'bg-primary text-primary-foreground hover:bg-primary-hover shadow-md hover:shadow-lg',
    secondary: 'bg-secondary text-secondary-foreground hover:bg-secondary-hover shadow-sm hover:shadow-md',
    outline: 'border border-primary text-primary bg-background hover:bg-primary hover:text-primary-foreground shadow-sm hover:shadow-md',
    ghost: 'text-primary hover:bg-primary/10 hover:text-primary',
    accent: 'bg-accent text-accent-foreground hover:bg-accent-hover shadow-md hover:shadow-lg',
    destructive: 'bg-destructive text-white hover:bg-destructive/90 shadow-md hover:shadow-lg'
  }

  const sizeClasses = {
    sm: 'px-3 py-1.5 text-sm h-8',
    md: 'px-4 py-2 h-10',
    lg: 'px-6 py-3 text-lg h-12'
  }

  const isDisabled = disabled || loading

  return (
    <motion.button
      className={`${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${className}`}
      whileHover={!isDisabled ? { scale: 1.05 } : undefined}
      whileTap={!isDisabled ? { scale: 0.95 } : undefined}
      transition={{ duration: 0.2 }}
      disabled={isDisabled}
      aria-busy={loading}
      aria-label={loading ? loadingText : undefined}
      {...props}
    >
      <span className={`flex items-center justify-center gap-2 ${loading ? 'opacity-0' : 'opacity-100'} transition-opacity duration-200`}>
        {icon && iconPosition === 'left' && <span className="flex-shrink-0">{icon}</span>}
        {children}
        {icon && iconPosition === 'right' && <span className="flex-shrink-0">{icon}</span>}
      </span>

      {loading && (
        <span className="absolute inset-0 flex items-center justify-center">
          <svg className="animate-spin h-4 w-4 text-current" fill="none" viewBox="0 0 24 24">
            <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
            <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
          </svg>
          <span className="ml-2 text-sm">{loadingText}</span>
        </span>
      )}
    </motion.button>
  )
}

// ============================================================================
// 网格组件
// ============================================================================

/**
 * 响应式网格组件
 */
interface ResponsiveGridProps {
  children: ReactNode
  className?: string
  cols?: {
    default?: number
    sm?: number
    md?: number
    lg?: number
    xl?: number
  }
  gap?: number
}

function ResponsiveGrid({
  children, 
  className = '', 
  cols = { default: 1, sm: 2, md: 3, lg: 4 },
  gap = 6 
}: ResponsiveGridProps) {
  const gridClasses = [
    `grid-cols-${cols.default || 1}`,
    cols.sm && `sm:grid-cols-${cols.sm}`,
    cols.md && `md:grid-cols-${cols.md}`,
    cols.lg && `lg:grid-cols-${cols.lg}`,
    cols.xl && `xl:grid-cols-${cols.xl}`,
    `gap-${gap}`
  ].filter(Boolean).join(' ')
  
  return (
    <div className={`grid ${gridClasses} ${className}`}>
      {children}
    </div>
  )
}

// ============================================================================
// 导出所有组件
// ============================================================================

export {
  // 布局
  ModernPageLayout,
  Container,
  
  // 卡片
  ModernCard,
  ImageCard,
  
  // 文字
  GradientHeading,
  Subtitle,
  
  // 表单
  ModernInput,
  ModernButton,
  
  // 网格
  ResponsiveGrid
}
