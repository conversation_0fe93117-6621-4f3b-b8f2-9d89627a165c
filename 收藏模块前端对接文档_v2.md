# 社团收藏功能集成文档

> 📝 **版本**: v2.0.0
> 🕒 **更新时间**: 2025-08-02
> 👥 **目标读者**: 前端开发者

## 📋 快速导航

- [🚀 快速开始](#-快速开始)
- [🔌 API 接口](#-api-接口)
- [🎯 个性化功能](#-个性化功能)
- [💻 前端集成](#-前端集成)
- [🎨 UI 组件示例](#-ui-组件示例)
- [🛡️ 安全注意事项](#️-安全注意事项)
- [❓ 常见问题](#-常见问题)

## 🚀 快速开始

### 基础信息

**功能定位**: 社团模块的增强功能，为社团列表提供个性化收藏体验
**核心价值**: 在社团浏览过程中提供收藏状态管理和个性化排序
**集成方式**: 无独立页面，完全集成在社团相关页面中
**主要场景**: 社团列表页面、社团详情页面、用户个人资料页面
**认证要求**: 收藏操作需要用户登录，个性化排序支持可选认证

> ⚠️ **重要说明**
> 收藏功能不应该有独立的 `/bookmarks` 页面路由。所有收藏相关的用户交互都应该集成在社团相关的页面中：
> - **社团列表页面**: 通过个性化排序展示收藏状态，已收藏的社团自动排在前面
> - **社团详情页面**: 提供收藏/取消收藏按钮
> - **用户个人资料**: 可以通过"仅显示已收藏"筛选来查看收藏的社团
>
> 这种设计确保了收藏功能作为社团模块的增强功能，而不是独立的功能模块。

### 5分钟上手

```typescript
// 1. 在社团列表中集成收藏功能
function CircleListPage() {
  const [page, setPage] = useState(1);

  // 获取个性化社团列表（已收藏的会自动排在前面）
  const { data: circles } = useQuery({
    queryKey: ['circles', page],
    queryFn: () => request(`/circles?page=${page}`),
  });

  return (
    <div className="circle-list">
      {circles?.items.map((circle) => (
        <CircleCard
          key={circle.id}
          circle={circle}
          // 收藏按钮集成在卡片中
          showBookmarkButton={true}
        />
      ))}
    </div>
  );
}

// 2. 社团卡片中的收藏按钮
function CircleCard({ circle, showBookmarkButton = true }) {
  const { data: status } = useBookmarkStatus(circle.id);
  const toggleMutation = useToggleBookmark();

  return (
    <div className="circle-card">
      <h3>{circle.name}</h3>
      {showBookmarkButton && (
        <BookmarkButton
          circleId={circle.id}
          isBookmarked={status?.isBookmarked}
          onToggle={() => toggleMutation.mutate(circle.id)}
        />
      )}
    </div>
  );
}
```

## 🔌 API 接口

### 接口概览

| 方法   | 路径                                  | 功能                     | 权限     | 使用场景                 |
| ------ | ------------------------------------- | ------------------------ | -------- | ------------------------ |
| `POST` | `/circles/{circleId}/bookmark`        | 切换收藏状态             | 登录用户 | 社团卡片、详情页收藏按钮 |
| `GET`  | `/circles/{circleId}/bookmark/status` | 检查收藏状态             | 登录用户 | 社团卡片状态显示         |
| `GET`  | `/circles` (增强)                     | 获取个性化社团列表       | 可选登录 | 社团列表页面主要接口     |
| `GET`  | `/user/bookmarks/stats`               | 获取收藏统计（用于优化） | 登录用户 | 批量状态检查优化         |

### 数据类型定义

```typescript
// 基础收藏对象
interface Bookmark {
  id: string;
  user_id: string;
  circle_id: string;
  created_at: string;
  updated_at: string;
}

// API 响应格式（项目标准）
interface ApiResponse<T> {
  code: number;
  message: string;
  data: T;
}

// 切换收藏响应
interface ToggleBookmarkResponse {
  isBookmarked: boolean;
}

// 收藏状态响应
interface BookmarkStatusResponse {
  isBookmarked: boolean;
  bookmarkId: string | null;
  createdAt: string | null;
}

// 收藏统计响应（用于性能优化）
interface BookmarkStatsResponse {
  total: number;
  bookmarkedCircleIds?: string[]; // 用于批量状态检查优化
}

// 个性化社团列表响应（增强版 /circles 接口）
interface PersonalizedCircleListResponse {
  items: CircleListItem[];
  total: number;
  page: number;
  pageSize: number;
  totalPages: number;
  // 个性化信息
  personalized: boolean; // 是否应用了个性化排序
  bookmarkedCount: number; // 当前页面中已收藏的数量
}

// 社团列表项（包含收藏状态）
interface CircleListItem {
  id: string;
  name: string;
  urls: string | null;
  created_at: string;
  updated_at: string;
  // 个性化字段
  isBookmarked?: boolean; // 仅在用户登录时返回
    circleId: string;
    reason: string;
  }>;
  total: number;
  successCount: number;
  failedCount: number;
}
```

### 详细接口说明

#### 1. 切换收藏状态

```typescript
POST /circles/{circleId}/bookmark

// 路径参数
{
  circleId: string; // 社团ID，如 'circle-123'
}

// 请求头
{
  "Authorization": "Bearer {token}" // 必需
}

// 响应示例
{
  "code": 0,
  "message": "已加入收藏", // 或 "已取消收藏"
  "data": {
    "isBookmarked": true // 当前收藏状态
  }
}
```

**功能说明**:

- 如果用户未收藏该社团，则添加收藏，返回 `isBookmarked: true`
- 如果用户已收藏该社团，则取消收藏，返回 `isBookmarked: false`
- 操作是原子性的，不会出现重复收藏的情况

#### 2. 检查收藏状态

```typescript
GET /circles/{circleId}/bookmark/status

// 路径参数
{
  circleId: string; // 社团ID
}

// 请求头
{
  "Authorization": "Bearer {token}" // 必需
}

// 响应示例
{
  "code": 0,
  "message": "获取收藏状态成功",
  "data": {
    "isBookmarked": true,
    "bookmarkId": "bookmark-uuid",
    "createdAt": "2025-01-01T00:00:00Z"
  }
}
```

#### 3. 获取个性化社团列表（增强版）

```typescript
GET /circles

// 查询参数
{
  page?: number;        // 页码，默认 1
  pageSize?: number;    // 每页数量，默认 20
  search?: string;      // 搜索关键词
  bookmarkedOnly?: boolean; // 仅显示已收藏的社团（用于个人资料页面）
}

// 请求头（可选，用于个性化排序）
{
  "Authorization": "Bearer {token}" // 可选，有则启用个性化排序
}

// 响应示例（已登录用户）
{
  "code": 0,
  "message": "获取社团列表成功",
  "data": {
    "items": [
      {
        "name": "某某工作室",
        "urls": "{\"twitter\":\"@example\"}",
        "created_at": "2025-01-01T00:00:00Z",
        "updated_at": "2025-01-01T00:00:00Z",
        "isBookmarked": true  // 仅在用户登录时返回
      },
      {
        "id": "circle-uuid-2",
        "name": "另一个工作室",
        "urls": null,
        "created_at": "2025-01-01T00:00:00Z",
        "updated_at": "2025-01-01T00:00:00Z",
        "isBookmarked": false // 仅在用户登录时返回
      }
    ],
    "total": 100,
    "page": 1,
    "pageSize": 20,
    "totalPages": 5,
    "personalized": true,    // 已应用个性化排序
    "bookmarkedCount": 8     // 当前页面中已收藏的数量
  }
}
```

#### 4. 获取收藏统计（性能优化接口）

```typescript
GET /user/bookmarks/stats?includeIds=true

// 查询参数
{
  includeIds?: boolean; // 是否包含收藏的社团ID列表，用于前端批量状态检查优化
}

// 请求头
{
  "Authorization": "Bearer {token}" // 必需
}

// 响应示例（includeIds=true，推荐用法）
{
  "code": 0,
  "message": "获取收藏统计成功",
  "data": {
    "total": 25,
    "bookmarkedCircleIds": [
      "circle-1", "circle-2", "circle-3", "circle-7", "circle-12"
      // ... 用户收藏的所有社团ID列表
    ]
  }
}
```

**使用场景**:
- 在社团列表页面初始化时调用，获取用户所有收藏的社团ID
- 前端可以基于这个ID列表快速判断每个社团的收藏状态，避免逐个调用状态检查接口
- 仅用于性能优化，不用于展示收藏列表
```

**性能优化说明**：

- `includeIds=true` 时返回收藏的社团ID列表，用于前端批量状态检查
- 可将原本需要50个单独请求的场景优化为1个请求
- 数据量相比完整收藏列表大幅减少（仅ID vs 完整社团信息）

## 🎯 个性化功能

### 社团列表个性化排序

**功能说明**：已登录用户在浏览社团列表时，已收藏的社团会自动排在前面，提升浏览体验。

#### API 端点

```typescript
GET / circles;
```

#### 鉴权方式

- **可选认证**：无需强制登录，支持渐进式增强
- **Session Cookie**：通过 `auth_session` cookie 自动识别用户身份
- **向后兼容**：未登录用户正常访问，已登录用户获得个性化体验

#### 排序规则

| 用户状态   | 排序方式            | 说明                                         |
| ---------- | ------------------- | -------------------------------------------- |
| **未登录** | 按社团名称排序      | 标准的字母顺序排列                           |
| **已登录** | 收藏优先 + 名称排序 | 已收藏社团排在前面，同等收藏状态下按名称排序 |

#### 使用示例

```typescript
// 1. 未登录用户 - 标准排序
const response = await fetch('/circles?page=1&pageSize=20');

// 2. 已登录用户 - 自动个性化排序
const response = await fetch('/circles?page=1&pageSize=20', {
  credentials: 'include', // 自动携带 session cookie
});

// 3. 使用 React Query
const { data: circles } = useQuery({
  queryKey: ['circles', page, pageSize, search],
  queryFn: () =>
    request<CircleListResponse>(
      `/circles?page=${page}&pageSize=${pageSize}&search=${search}`
    ),
  staleTime: 5 * 60 * 1000, // 5分钟缓存
});
```

#### 响应格式

```json
{
  "items": [
    // 已收藏的社团排在前面
    {
      "id": "circle-1",
      "name": "已收藏社团A",
      "urls": "{\"twitter\":\"@example\"}",
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z"
    },
    {
      "id": "circle-2",
      "name": "已收藏社团B",
      "urls": "{\"website\":\"https://example.com\"}",
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z"
    },
    // 然后是未收藏的社团
    {
      "id": "circle-3",
      "name": "未收藏社团C",
      "urls": null,
      "created_at": "2025-01-01T00:00:00Z",
      "updated_at": "2025-01-01T00:00:00Z"
    }
  ],
  "total": 100,
  "page": 1,
  "pageSize": 20
}
```

#### 性能特点

- **缓存策略**：未登录用户结果使用全局缓存，已登录用户实时查询
- **数据库优化**：单次SQL查询完成排序，利用现有索引
- **网络开销**：无额外请求，响应格式保持一致

#### 前端集成建议

```typescript
// 推荐：结合收藏状态管理的完整方案
function CircleListPage() {
  const [page, setPage] = useState(1);
  const { isBookmarked, toggleBookmark } = useBookmarkManager();

  // 获取社团列表（自动个性化排序）
  const { data: circles, isLoading } = useQuery({
    queryKey: ['circles', page],
    queryFn: () => request<CircleListResponse>(`/circles?page=${page}`),
  });

  return (
    <div className="circle-list">
      {circles?.items.map((circle) => (
        <CircleCard
          key={circle.id}
          circle={circle}
          isBookmarked={isBookmarked(circle.id)}
          onToggleBookmark={() => toggleBookmark(circle.id)}
        />
      ))}
    </div>
  );
}
```

## 💻 前端集成

### React Query Hooks

```typescript
// hooks/useBookmark.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { request } from '@/lib/http';

// 切换收藏状态
export function useToggleBookmark() {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (circleId: string) => {
      return request<ToggleBookmarkResponse>(`/circles/${circleId}/bookmark`, {
        method: 'POST',
      });
    },
    onSuccess: (data, circleId) => {
      // 更新收藏状态缓存
      queryClient.setQueryData(['bookmark-status', circleId], {
        isBookmarked: data.isBookmarked,
        bookmarkId: data.isBookmarked ? 'temp-id' : null,
        createdAt: data.isBookmarked ? new Date().toISOString() : null,
      });

      // 刷新收藏列表和统计
      queryClient.invalidateQueries({ queryKey: ['bookmarks'] });
      queryClient.invalidateQueries({ queryKey: ['bookmark-stats'] });
    },
  });
}

// 检查收藏状态
export function useBookmarkStatus(circleId: string) {
  return useQuery({
    queryKey: ['bookmark-status', circleId],
    queryFn: () =>
      request<BookmarkStatusResponse>(`/circles/${circleId}/bookmark/status`),
    staleTime: 10 * 60 * 1000, // 10分钟缓存
    enabled: !!circleId,
  });
}

// 获取个性化社团列表
export function usePersonalizedCircles(query: { page?: number; pageSize?: number; search?: string; bookmarkedOnly?: boolean }) {
  return useQuery({
    queryKey: ['circles', query],
    queryFn: () => {
      const params = new URLSearchParams();
      Object.entries(query).forEach(([key, value]) => {
        if (value !== undefined) {
          params.append(key, String(value));
        }
      });
      return request<PersonalizedCircleListResponse>(`/circles?${params}`);
    },
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
}

// 获取收藏统计（用于性能优化）
export function useBookmarkStats(includeIds = true) {
  return useQuery({
    queryKey: ['bookmark-stats', includeIds],
    queryFn: () => request<BookmarkStatsResponse>(`/user/bookmarks/stats?includeIds=${includeIds}`),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });
}

// 🚀 收藏状态管理器（推荐用法）
export function useBookmarkManager() {
  const queryClient = useQueryClient();

  // 获取收藏统计（包含ID列表）
  const { data: stats, isLoading } = useQuery({
    queryKey: ['bookmark-stats-with-ids'],
    queryFn: () =>
      request<BookmarkStatsResponse>('/user/bookmarks/stats?includeIds=true'),
    staleTime: 5 * 60 * 1000, // 5分钟缓存
  });

  // 创建收藏状态映射
  const bookmarkedIds = useMemo(() => {
    return new Set(stats?.bookmarkedCircleIds || []);
  }, [stats?.bookmarkedCircleIds]);

  // 快速查询收藏状态
  const isBookmarked = useCallback(
    (circleId: string) => {
      return bookmarkedIds.has(circleId);
    },
    [bookmarkedIds]
  );

  // 切换收藏状态（带乐观更新）
  const toggleBookmark = useMutation({
    mutationFn: (circleId: string) =>
      request(`/circles/${circleId}/bookmark`, { method: 'POST' }),

    onMutate: async (circleId) => {
      // 乐观更新：立即更新UI
      await queryClient.cancelQueries({
        queryKey: ['bookmark-stats-with-ids'],
      });

      const previousStats = queryClient.getQueryData([
        'bookmark-stats-with-ids',
      ]);

      queryClient.setQueryData(['bookmark-stats-with-ids'], (old: any) => {
        if (!old) return old;

        const currentIds = new Set(old.bookmarkedCircleIds || []);
        const wasBookmarked = currentIds.has(circleId);

        if (wasBookmarked) {
          currentIds.delete(circleId);
        } else {
          currentIds.add(circleId);
        }

        return {
          ...old,
          totalBookmarks: old.totalBookmarks + (wasBookmarked ? -1 : 1),
          bookmarkedCircleIds: Array.from(currentIds),
        };
      });

      return { previousStats };
    },

    onError: (err, circleId, context) => {
      // 回滚乐观更新
      if (context?.previousStats) {
        queryClient.setQueryData(
          ['bookmark-stats-with-ids'],
          context.previousStats
        );
      }
    },

    onSettled: () => {
      // 确保数据同步
      queryClient.invalidateQueries({ queryKey: ['bookmark-stats-with-ids'] });
    },
  });

  return {
    isBookmarked,
    toggleBookmark: toggleBookmark.mutate,
    isLoading,
    isToggling: toggleBookmark.isPending,
    stats,
  };
}
```

### 错误处理

```typescript
// utils/bookmarkErrorHandler.ts
export function handleBookmarkError(error: any) {
  if (error?.code === 20001) {
    return '请先登录后再进行收藏操作';
  }
  if (error?.code === 40001) {
    return '社团ID无效';
  }
  if (error?.code === 10002) {
    return '社团不存在';
  }
  if (error?.code === 50001) {
    return '服务器错误，请重试';
  }
  return '操作失败，请重试';
}

// 在组件中使用
const toggleMutation = useToggleBookmark();

const handleToggle = async (circleId: string) => {
  try {
    await toggleMutation.mutateAsync(circleId);
    toast.success('操作成功');
  } catch (error) {
    const message = handleBookmarkError(error);
    toast.error(message);
  }
};
```

### 用户个人资料页面集成

```typescript
// 在用户个人资料页面显示收藏的社团
function UserProfilePage() {
  const [showBookmarkedOnly, setShowBookmarkedOnly] = useState(false);

  // 使用 bookmarkedOnly 参数筛选收藏的社团
  const { data: circles } = usePersonalizedCircles({
    page: 1,
    pageSize: 20,
    bookmarkedOnly: showBookmarkedOnly
  });

  return (
    <div className="user-profile">
      <div className="filter-controls">
        <label>
          <input
            type="checkbox"
            checked={showBookmarkedOnly}
            onChange={(e) => setShowBookmarkedOnly(e.target.checked)}
          />
          仅显示已收藏的社团
        </label>
      </div>

      <div className="circles-grid">
        {circles?.items.map((circle) => (
          <CircleCard key={circle.id} circle={circle} />
        ))}
      </div>
    </div>
  );
}
```

## 🎨 前端集成要点

### 个性化社团列表集成

**核心API调用**：

```typescript
// 获取个性化社团列表
const response = await fetch('/circles?page=1&pageSize=20', {
  credentials: 'include', // 携带认证信息
});
```

**集成要点**：

- 使用 `credentials: 'include'` 自动携带session cookie
- 已登录用户自动获得个性化排序
- 未登录用户正常访问，无需特殊处理
- 结合收藏状态管理实现完整体验

### 基础收藏按钮

```tsx
// 简单的收藏按钮示例
function BookmarkButton({ circleId }: { circleId: string }) {
  const { data: isBookmarked } = useBookmarkStatus(circleId);
  const toggleMutation = useToggleBookmark();

  return (
    <button onClick={() => toggleMutation.mutate(circleId)}>
      {isBookmarked ? '已收藏' : '收藏'}
    </button>
  );
```

## 🛡️ 安全注意事项

### 认证要求

```typescript
// 确保用户已登录
import { useAuth } from '@/hooks/useAuth';

function BookmarkFeature({ circleId }: { circleId: string }) {
  const { isAuthenticated, user } = useAuth();

  if (!isAuthenticated) {
    return (
      <Button variant="outline" onClick={() => router.push('/login')}>
        登录后收藏
      </Button>
    );
  }

  // 已登录用户显示收藏功能
  return <BookmarkButton circleId={circleId} />;
}
```

### 数据验证

```typescript
// 验证收藏操作的数据完整性
function validateBookmarkData(circleId: string) {
  if (!circleId || typeof circleId !== 'string') {
    throw new Error('Invalid circle ID');
  }

  if (circleId.length < 1 || circleId.length > 50) {
    throw new Error('Circle ID length invalid');
  }
}
```

## ❓ 常见问题

### Q: 个性化排序是否会影响性能？

**A**: 性能影响很小，已做优化：

- **未登录用户**：使用全局缓存，性能无影响
- **已登录用户**：数据库层面排序，单次SQL查询完成
- **缓存策略**：个性化结果不缓存，避免缓存污染
- **索引优化**：利用现有的bookmarks表索引，查询效率高

### Q: 个性化排序的逻辑是什么？

**A**: 排序规则如下：

1. **已收藏社团**：排在列表前面
2. **未收藏社团**：排在已收藏社团后面
3. **同等收藏状态**：按社团名称字母顺序排列
4. **搜索结果**：保持相同的个性化排序逻辑

### Q: 如何在前端判断是否启用了个性化排序？

**A**: 通过用户登录状态判断：

```typescript
// 检查用户是否登录
const { user } = useAuth();
const isPersonalized = !!user;

// 在UI中显示提示
{isPersonalized && (
  <Badge variant="secondary">
    个性化排序已启用
  </Badge>
)}
```

### Q: 个性化排序是否支持搜索？

**A**: 完全支持，搜索结果同样会应用个性化排序：

```typescript
// 搜索时保持个性化排序
const { data } = useQuery({
  queryKey: ['circles', search],
  queryFn: () => request(`/circles?search=${search}`),
  // 已收藏的匹配结果会排在前面
});
```

### Q: 如何处理用户登录状态变化？

**A**: 监听认证状态变化，清理或同步收藏数据：

```typescript
// 监听登录状态变化
useEffect(() => {
  if (isAuthenticated) {
    // 用户登录后，重新获取个性化数据
    queryClient.invalidateQueries({ queryKey: ['circles'] });
    queryClient.invalidateQueries({ queryKey: ['bookmarks'] });
  } else {
    // 用户登出后，清理个人数据缓存
    queryClient.removeQueries({ queryKey: ['bookmarks'] });
    queryClient.removeQueries({ queryKey: ['bookmark-stats'] });
  }
}, [isAuthenticated]);
```

---

## 🎯 设计理念总结

### 为什么不设计独立的收藏页面？

1. **用户体验一致性**: 收藏功能作为社团浏览的增强，保持在社团上下文中更自然
2. **减少页面跳转**: 用户可以在社团列表中直接管理收藏，无需额外的页面切换
3. **个性化体验**: 通过个性化排序，已收藏的社团自动排在前面，提供更好的浏览体验
4. **架构简洁性**: 避免功能模块过度分离，保持代码库的简洁和可维护性

### 推荐的用户交互流程

1. **社团发现**: 用户在社团列表页面浏览和搜索社团
2. **收藏管理**: 直接在社团卡片上进行收藏/取消收藏操作
3. **个性化浏览**: 已收藏的社团自动排在前面，提升浏览效率
4. **收藏查看**: 在个人资料页面通过筛选查看收藏的社团

这种设计确保了收藏功能与社团模块的紧密集成，提供了更流畅的用户体验。

---

## 📚 相关文档

- [API 规范文档](./api-specification.md)
- [社团模块文档](./circle-module.md)
- [用户认证文档](./auth-module.md)
- [错误处理指南](./error-handling.md)

---

**📞 技术支持**: 如有问题请联系后端团队或查看项目 Issues
