'use client';

import React, { useState } from 'react';
import { usePathname } from 'next/navigation';
import Link from 'next/link';
import { motion, AnimatePresence } from 'motion/react';
import { 
  User, 
  Settings, 
  Bookmark, 
  Activity, 
  Menu, 
  X,
  LogOut,
  ChevronRight
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Badge } from '@/components/ui/badge';
import { useAuth } from '@/contexts/user';
import type { User as UserType } from '@/types/user';

interface UserCenterLayoutProps {
  children: React.ReactNode;
  user: UserType | null;
}

const navigationItems = [
  {
    href: '/profile',
    label: 'Profile',
    labelZh: '个人资料',
    icon: User,
    description: 'Manage your personal information',
    descriptionZh: '管理您的个人信息',
  },
  {
    href: '/profile/bookmarks',
    label: 'Bookmarks',
    labelZh: '收藏',
    icon: Bookmark,
    description: 'Your saved events and circles',
    descriptionZh: '您收藏的事件和社团',
  },
  {
    href: '/profile/activity',
    label: 'Activity',
    labelZh: '活动记录',
    icon: Activity,
    description: 'Your browsing and interaction history',
    descriptionZh: '您的浏览和互动历史',
  },
  {
    href: '/profile/settings',
    label: 'Settings',
    labelZh: '设置',
    icon: Settings,
    description: 'Preferences and account settings',
    descriptionZh: '偏好设置和账户设置',
  },
];

export function UserCenterLayout({ children, user }: UserCenterLayoutProps) {
  const pathname = usePathname();
  const { logout } = useAuth();
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const handleLogout = async () => {
    await logout();
  };

  const getUserInitials = (username: string) => {
    return username.slice(0, 2).toUpperCase();
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin':
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case 'editor':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200';
      case 'viewer':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-200';
    }
  };

  return (
    <div className="min-h-screen news-gradient-bg vintage-paper-texture relative overflow-hidden">
      {/* Background decorative elements - 老式报纸风格 */}
      <div className="absolute inset-0 overflow-hidden pointer-events-none">
        <div className="absolute -top-40 -right-40 w-80 h-80 bg-gradient-to-br from-stone-400/5 to-stone-600/5 rounded-full blur-3xl"></div>
        <div className="absolute -bottom-40 -left-40 w-80 h-80 bg-gradient-to-br from-stone-400/5 to-stone-600/5 rounded-full blur-3xl"></div>
      </div>

      {/* Mobile Header */}
      <div className="lg:hidden sticky top-16 z-40 bg-white/80 dark:bg-stone-900/80 backdrop-blur-md border-b border-stone-200 dark:border-stone-700">
        <div className="flex items-center justify-between p-4">
          <div className="flex items-center gap-3">
            <Avatar className="h-8 w-8">
              <AvatarImage src="" alt={user?.username} />
              <AvatarFallback className="bg-gradient-to-br from-red-500 to-red-700 text-white text-sm">
                {user?.username ? getUserInitials(user.username) : 'U'}
              </AvatarFallback>
            </Avatar>
            <div>
              <p className="font-medium text-sm">{user?.username}</p>
              <Badge variant="secondary" className={`text-xs ${getRoleColor(user?.role || '')}`}>
                {user?.role}
              </Badge>
            </div>
          </div>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
          </Button>
        </div>
      </div>

      <div className="flex">
        {/* Desktop Sidebar */}
        <motion.aside
          className="hidden lg:flex lg:flex-col lg:w-80 lg:fixed lg:top-16 lg:bottom-0 lg:left-0 lg:z-40"
          initial={{ x: -320 }}
          animate={{ x: 0 }}
          transition={{ duration: 0.3, ease: "easeOut" }}
        >
          <div className="flex flex-col h-full bg-white/60 dark:bg-stone-900/60 backdrop-blur-xl border-r border-stone-200/50 dark:border-stone-700/50">
            {/* User Profile Header */}
            <div className="p-8 border-b border-stone-200/50 dark:border-stone-700/50">
              <motion.div 
                className="flex flex-col items-center text-center"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: 0.1 }}
              >
                <Avatar className="h-20 w-20 mb-4 ring-4 ring-white/50 dark:ring-stone-800/50">
                  <AvatarImage src="" alt={user?.username} />
                  <AvatarFallback className="bg-gradient-to-br from-red-500 via-red-600 to-red-700 text-white text-xl font-semibold">
                    {user?.username ? getUserInitials(user.username) : 'U'}
                  </AvatarFallback>
                </Avatar>
                <h2 className="text-xl font-bold text-stone-900 dark:text-stone-100 mb-1">
                  {user?.username}
                </h2>
                <Badge variant="secondary" className={`mb-2 ${getRoleColor(user?.role || '')}`}>
                  {user?.role}
                </Badge>
                <p className="text-sm text-stone-600 dark:text-stone-400">
                  Welcome to your personal space
                </p>
              </motion.div>
            </div>

            {/* Navigation */}
            <nav className="flex-1 p-6">
              <div className="space-y-2">
                {navigationItems.map((item, index) => {
                  const Icon = item.icon;
                  const isActive = pathname === item.href;
                  
                  return (
                    <motion.div
                      key={item.href}
                      initial={{ opacity: 0, x: -20 }}
                      animate={{ opacity: 1, x: 0 }}
                      transition={{ delay: 0.1 + index * 0.05 }}
                    >
                      <Link href={item.href}>
                        <Card className={`transition-all duration-200 hover:shadow-md cursor-pointer group ${
                          isActive
                            ? 'bg-gradient-to-r from-red-50 to-red-50 dark:from-red-950 dark:to-red-950 border-red-200 dark:border-red-800'
                            : 'hover:bg-stone-50 dark:hover:bg-stone-800/50'
                        }`}>
                          <CardContent className="p-4">
                            <div className="flex items-center gap-3">
                              <div className={`p-2 rounded-lg transition-colors ${
                                isActive
                                  ? 'bg-gradient-to-br from-red-500 to-red-600 text-white'
                                  : 'bg-stone-100 dark:bg-stone-800 text-stone-600 dark:text-stone-400 group-hover:bg-stone-200 dark:group-hover:bg-stone-700'
                              }`}>
                                <Icon className="h-4 w-4" />
                              </div>
                              <div className="flex-1">
                                <p className={`font-medium text-sm ${
                                  isActive ? 'text-red-900 dark:text-red-100' : 'text-stone-900 dark:text-stone-100'
                                }`}>
                                  {item.labelZh}
                                </p>
                                <p className="text-xs text-stone-500 dark:text-stone-400">
                                  {item.descriptionZh}
                                </p>
                              </div>
                              <ChevronRight className={`h-4 w-4 transition-transform ${
                                isActive ? 'text-red-600 dark:text-red-400' : 'text-stone-400 group-hover:translate-x-1'
                              }`} />
                            </div>
                          </CardContent>
                        </Card>
                      </Link>
                    </motion.div>
                  );
                })}
              </div>
            </nav>

            {/* Logout Button */}
            <div className="p-6 border-t border-stone-200/50 dark:border-stone-700/50">
              <Button
                variant="outline"
                onClick={handleLogout}
                className="w-full justify-start gap-2 text-stone-600 dark:text-stone-400 hover:text-red-600 dark:hover:text-red-400 hover:border-red-200 dark:hover:border-red-800"
              >
                <LogOut className="h-4 w-4" />
                退出登录
              </Button>
            </div>
          </div>
        </motion.aside>

        {/* Mobile Menu */}
        <AnimatePresence>
          {isMobileMenuOpen && (
            <motion.div
              className="lg:hidden fixed inset-0 z-40 bg-black/50"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
              onClick={() => setIsMobileMenuOpen(false)}
            >
              <motion.div
                className="fixed top-16 bottom-0 left-0 w-80 bg-white dark:bg-stone-900 shadow-xl"
                initial={{ x: -320 }}
                animate={{ x: 0 }}
                exit={{ x: -320 }}
                transition={{ type: "spring", damping: 25, stiffness: 200 }}
                onClick={(e) => e.stopPropagation()}
              >
                {/* Mobile navigation content - same as desktop but adapted */}
                <div className="flex flex-col h-full">
                  <div className="p-6 border-b border-stone-200 dark:border-stone-700">
                    <div className="flex items-center gap-3">
                      <Avatar className="h-12 w-12">
                        <AvatarImage src="" alt={user?.username} />
                        <AvatarFallback className="bg-gradient-to-br from-red-500 to-red-700 text-white">
                          {user?.username ? getUserInitials(user.username) : 'U'}
                        </AvatarFallback>
                      </Avatar>
                      <div>
                        <h3 className="font-semibold">{user?.username}</h3>
                        <Badge variant="secondary" className={`text-xs ${getRoleColor(user?.role || '')}`}>
                          {user?.role}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  
                  <nav className="flex-1 p-4">
                    <div className="space-y-2">
                      {navigationItems.map((item) => {
                        const Icon = item.icon;
                        const isActive = pathname === item.href;
                        
                        return (
                          <Link 
                            key={item.href} 
                            href={item.href}
                            onClick={() => setIsMobileMenuOpen(false)}
                          >
                            <div className={`flex items-center gap-3 p-3 rounded-lg transition-colors ${
                              isActive
                                ? 'bg-red-50 dark:bg-red-950 text-red-900 dark:text-red-100'
                                : 'hover:bg-stone-50 dark:hover:bg-stone-800'
                            }`}>
                              <Icon className="h-5 w-5" />
                              <span className="font-medium">{item.labelZh}</span>
                            </div>
                          </Link>
                        );
                      })}
                    </div>
                  </nav>
                  
                  <div className="p-4 border-t border-stone-200 dark:border-stone-700">
                    <Button
                      variant="outline"
                      onClick={handleLogout}
                      className="w-full justify-start gap-2"
                    >
                      <LogOut className="h-4 w-4" />
                      退出登录
                    </Button>
                  </div>
                </div>
              </motion.div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Main Content */}
        <main className="flex-1 lg:ml-80 relative pt-16">
          <div className="p-6 lg:p-8">
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, ease: "easeOut" }}
              className="relative z-10"
            >
              {children}
            </motion.div>
          </div>

          {/* Floating action elements */}
          <motion.div
            className="fixed bottom-6 right-6 lg:bottom-8 lg:right-8 z-20"
            initial={{ scale: 0, opacity: 0 }}
            animate={{ scale: 1, opacity: 1 }}
            transition={{ delay: 1, duration: 0.3, type: "spring" }}
          >
            <Button
              size="sm"
              className="rounded-full shadow-lg bg-gradient-to-r from-red-500 to-red-600 hover:from-red-600 hover:to-red-700 text-white border-0"
              onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}
            >
              <ChevronRight className="h-4 w-4 rotate-[-90deg]" />
            </Button>
          </motion.div>
        </main>
      </div>
    </div>
  );
}
