/**
 * 认证状态管理
 * 基于后端文档包的认证集成规范
 */

import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import {
  authService,
  type LoginCredentials,
  type RegisterData,
} from '@/services/auth';
import type { User } from '@/types/user';

interface AuthState {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;

  // Actions
  login: (credentials: LoginCredentials | { identifier: string; password: string }) => Promise<void>;
  register: (userData: RegisterData) => Promise<void>;
  logout: () => Promise<void>;
  refreshUser: () => Promise<void>;
  checkAuth: (silent?: boolean) => Promise<void>;
}

export const useAuthStore = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      isAuthenticated: false,
      isLoading: false,

      login: async (credentials) => {
        set({ isLoading: true });
        try {
          // 如果credentials有email字段，使用邮箱登录；否则使用智能登录
          let result;
          if ('email' in credentials) {
            result = await authService.login(credentials);
          } else {
            // 假设是 {identifier, password} 格式
            result = await authService.smartLogin(credentials.identifier, credentials.password);
          }
          set({
            user: result.user,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      register: async (userData) => {
        set({ isLoading: true });
        try {
          const result = await authService.register(userData);
          set({
            user: result.user,
            isAuthenticated: true,
            isLoading: false,
          });
        } catch (error) {
          set({ isLoading: false });
          throw error;
        }
      },

      logout: async () => {
        set({ isLoading: true });
        try {
          await authService.logout();
        } catch (error) {
          // 登出失败不应该阻止本地状态清除
          console.warn('[AuthStore] 登出过程中出现错误，但继续清除本地状态:', error);
        } finally {
          set({
            user: null,
            isAuthenticated: false,
            isLoading: false,
          });
        }
      },

      refreshUser: async () => {
        if (!get().isAuthenticated) return;

        try {
          const user = await authService.getCurrentUser();
          set({ user });
        } catch (error) {
          // Token可能已过期，执行登出
          get().logout();
        }
      },

      checkAuth: async (silent: boolean = false) => {
        try {
          // 直接调用 /auth/me 检查cookie认证状态
          // 不依赖localStorage的token，因为后端使用cookie认证
          // silent模式下不会在控制台显示401错误
          const currentUser = await authService.getCurrentUser(silent);

          console.debug('[AuthStore] checkAuth 获取到用户:', currentUser);
          console.debug('[AuthStore] checkAuth 用户的 role:', currentUser.role);

          // 如果成功获取用户信息，说明cookie有效
          set({
            user: currentUser,
            isAuthenticated: true
          });

          console.debug('[AuthStore] checkAuth 设置完成，当前 store 状态:', get());
        } catch (error) {
          // 只有当 /auth/me 返回401时，才说明cookie无效
          // 清除认证信息
          authService.clearAuth();
          set({
            user: null,
            isAuthenticated: false,
          });
        }
      },
    }),
    {
      name: 'auth-storage',
      partialize: (state) => ({
        user: state.user,
        isAuthenticated: state.isAuthenticated,
      }),
      // 添加版本控制，确保存储格式兼容
      version: 1,
      // 自定义合并策略，页面刷新时优先使用服务端状态
      merge: (persistedState, currentState) => ({
        ...currentState,
        ...(persistedState || {}),
        // 页面刷新时重置加载状态，强制重新检查认证
        isLoading: true,
      }),
    }
  )
);
