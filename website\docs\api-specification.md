# 多语言 API 规范

## 📋 通用规范

### 请求头部

| 头部名称 | 类型 | 必需 | 说明 | 示例 |
|----------|------|------|------|------|
| `Accept-Language` | string | 否 | 标准语言偏好 | `zh`, `ja`, `en` |
| `X-Locale` | string | 否 | 明确指定语言 | `zh`, `ja`, `en` |
| `Content-Type` | string | 是 | 内容类型 | `application/json` |

### 响应头部

| 头部名称 | 类型 | 说明 | 示例 |
|----------|------|------|------|
| `Content-Language` | string | 响应内容语言 | `zh` |
| `X-Response-Locale` | string | 实际使用的语言 | `zh` |

### 标准响应格式

```json
{
  "success": true,
  "data": [...],
  "locale": "zh",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "meta": {
    "total": 100,
    "page": 1,
    "limit": 20,
    "hasMore": true
  }
}
```

### 错误响应格式

```json
{
  "success": false,
  "error": {
    "code": "NOT_FOUND",
    "message": "未找到相关内容"
  },
  "locale": "zh",
  "timestamp": "2024-01-15T10:30:00.000Z"
}
```

## 🎯 API 端点

### 1. 获取事件列表

```
GET /api/events
```

#### 请求参数

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `page` | integer | 否 | 1 | 页码 |
| `limit` | integer | 否 | 20 | 每页数量 |
| `category` | string | 否 | - | 事件分类 |

#### 请求示例

```bash
curl -X GET "https://api.ayafeed.com/api/events?page=1&limit=20" \
  -H "X-Locale: zh" \
  -H "Accept-Language: zh"
```

#### 响应示例

```json
{
  "success": true,
  "data": [
    {
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "name": "Comiket 103",
      "description": "世界最大的同人志即卖会",
      "venue_name": "东京国际展示场",
      "venue_lat": 35.6301,
      "venue_lng": 139.7930,
      "start_date": "2024-12-30T10:00:00Z",
      "end_date": "2024-12-31T16:00:00Z",
      "image_url": "https://example.com/comiket103.jpg",
      "url": "https://www.comiket.co.jp/"
    }
  ],
  "locale": "zh",
  "timestamp": "2024-01-15T10:30:00.000Z",
  "meta": {
    "total": 150,
    "page": 1,
    "limit": 20,
    "hasMore": true
  }
}
```

### 2. 获取事件详情

```
GET /api/events/{id}
```

#### 路径参数

| 参数 | 类型 | 必需 | 说明 |
|------|------|------|------|
| `id` | string | 是 | 事件 ID |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "id": "550e8400-e29b-41d4-a716-446655440000",
    "name": "Comiket 103",
    "description": "世界最大的同人志即卖会，每年举办两次...",
    "venue_name": "东京国际展示场",
    "venue_lat": 35.6301,
    "venue_lng": 139.7930,
    "start_date": "2024-12-30T10:00:00Z",
    "end_date": "2024-12-31T16:00:00Z",
    "image_url": "https://example.com/comiket103.jpg",
    "url": "https://www.comiket.co.jp/",
    "category": "doujin",
    "participating_circles": 35000
  },
  "locale": "zh"
}
```

### 3. 获取社团列表

```
GET /api/circles
```

#### 请求参数

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `page` | integer | 否 | 1 | 页码 |
| `limit` | integer | 否 | 20 | 每页数量 |
| `tags` | string | 否 | - | 标签过滤（逗号分隔） |

#### 响应示例

```json
{
  "success": true,
  "data": [
    {
      "id": "660e8400-e29b-41d4-a716-446655440001",
      "name": "上海アリス幻樂団",
      "description": "东方Project 系列游戏的制作团体",
      "tags": ["东方Project", "游戏", "音乐"],
      "website_url": "https://www16.big.or.jp/~zun/",
      "twitter_handle": "@korindo"
    }
  ],
  "locale": "zh",
  "meta": {
    "total": 500,
    "page": 1,
    "limit": 20,
    "hasMore": true
  }
}
```

### 4. 搜索

```
GET /api/search
```

#### 请求参数

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `q` | string | 是 | - | 搜索关键词 |
| `type` | string | 否 | all | 搜索类型：all, events, circles |
| `page` | integer | 否 | 1 | 页码 |
| `limit` | integer | 否 | 20 | 每页数量 |

#### 请求示例

```bash
curl -X GET "https://api.ayafeed.com/api/search?q=Comiket&type=events" \
  -H "X-Locale: zh"
```

#### 响应示例

```json
{
  "success": true,
  "data": [
    {
      "type": "event",
      "id": "550e8400-e29b-41d4-a716-446655440000",
      "name": "Comiket 103",
      "description": "世界最大的同人志即卖会",
      "venue_name": "东京国际展示场",
      "start_date": "2024-12-30T10:00:00Z",
      "image_url": "https://example.com/comiket103.jpg",
      "rank": 0.8567
    }
  ],
  "locale": "zh",
  "meta": {
    "total": 15,
    "query": "Comiket",
    "type": "events"
  }
}
```

### 5. 获取 Feed 流

```
GET /api/feed
```

#### 请求参数

| 参数 | 类型 | 必需 | 默认值 | 说明 |
|------|------|------|--------|------|
| `page` | integer | 否 | 1 | 页码 |
| `limit` | integer | 否 | 20 | 每页数量 |
| `type` | string | 否 | all | 内容类型：all, events, circles |

#### 响应示例

```json
{
  "success": true,
  "data": [
    {
      "id": "feed-001",
      "type": "event",
      "content": {
        "id": "550e8400-e29b-41d4-a716-446655440000",
        "name": "Comiket 103",
        "description": "世界最大的同人志即卖会",
        "start_date": "2024-12-30T10:00:00Z"
      },
      "created_at": "2024-01-15T08:00:00Z"
    }
  ],
  "locale": "zh",
  "meta": {
    "total": 200,
    "page": 1,
    "limit": 20,
    "hasMore": true
  }
}
```

## 🔒 认证相关 API

### 1. 用户登录

```
POST /auth/login
```

#### 请求体

```json
{
  "username": "<EMAIL>",
  "password": "password123"
}
```

#### 响应示例

```json
{
  "success": true,
  "data": {
    "user": {
      "id": "user-001",
      "username": "<EMAIL>",
      "email": "<EMAIL>"
    },
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9..."
  },
  "locale": "zh"
}
```

### 2. 获取用户信息

```
GET /api/user
```

#### 请求头部

| 头部名称 | 类型 | 必需 | 说明 |
|----------|------|------|------|
| `Authorization` | string | 是 | Bearer token |

#### 响应示例

```json
{
  "success": true,
  "data": {
    "id": "user-001",
    "username": "<EMAIL>",
    "email": "<EMAIL>",
    "role": "user",
    "created_at": "2024-01-01T00:00:00Z"
  }
}
```

## 📊 状态码说明

| 状态码 | 说明 | 示例场景 |
|--------|------|----------|
| 200 | 成功 | 正常返回数据 |
| 400 | 请求错误 | 参数格式错误 |
| 401 | 未授权 | 需要登录 |
| 403 | 禁止访问 | 权限不足 |
| 404 | 未找到 | 资源不存在 |
| 500 | 服务器错误 | 内部错误 |

## 🌐 语言支持

### 支持的语言代码

| 代码 | 语言 | 说明 |
|------|------|------|
| `zh` | 中文 | 简体中文 |
| `ja` | 日文 | 日本語 |
| `en` | 英文 | English |

### 语言检测优先级

1. `X-Locale` 请求头（最高优先级）
2. `Accept-Language` 请求头
3. 默认语言（`zh`）

### 语言相关的缓存键

```
events:zh:page:1:limit:20
circles:ja:tags:anime
search:en:query:comiket:type:events
```

## 🔧 开发工具

### Postman 集合

```json
{
  "info": {
    "name": "Ayafeed API",
    "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"
  },
  "variable": [
    {
      "key": "baseUrl",
      "value": "https://api.ayafeed.com"
    },
    {
      "key": "locale",
      "value": "zh"
    }
  ],
  "event": [
    {
      "listen": "prerequest",
      "script": {
        "exec": [
          "pm.request.headers.add({",
          "  key: 'X-Locale',",
          "  value: pm.variables.get('locale')",
          "});"
        ]
      }
    }
  ]
}
```

这个 API 规范确保了前后端在多语言处理上的一致性！
