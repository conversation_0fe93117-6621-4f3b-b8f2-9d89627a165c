/**
 * Better Auth 客户端实例
 * 使用官方 React 客户端，提供类型安全的认证功能
 */

import { createAuthClient } from "better-auth/react";
import { usernameClient } from "better-auth/client/plugins";

// 创建 Better Auth 客户端实例
export const authClient = createAuthClient({
  // 设置完整的后端 URL，包含正确的路径
  // 后端路径是 /auth，不是默认的 /api/auth
  baseURL: process.env.NEXT_PUBLIC_API_URL
    ? `${process.env.NEXT_PUBLIC_API_URL}/auth`
    : "http://localhost:8787/auth",
  plugins: [
    usernameClient(), // 添加用户名登录支持
  ],
});

// 导出常用的方法，方便使用
export const { 
  signIn, 
  signUp, 
  signOut, 
  useSession,
  getSession,
  updateUser,
  changePassword,
  resetPassword,
  sendVerificationEmail,
  verifyEmail,
} = authClient;

// 导出完整的客户端实例，用于高级用法
export default authClient;
