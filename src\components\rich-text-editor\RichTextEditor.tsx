'use client';

import React, { useCallback, useEffect } from 'react';
import { useEditor, EditorContent } from '@tiptap/react';
import StarterKit from '@tiptap/starter-kit';
import Image from '@tiptap/extension-image';
import Link from '@tiptap/extension-link';
import TextAlign from '@tiptap/extension-text-align';
import { Color } from '@tiptap/extension-color';
import { TextStyle } from '@tiptap/extension-text-style';
import BulletList from '@tiptap/extension-bullet-list';
import OrderedList from '@tiptap/extension-ordered-list';
import ListItem from '@tiptap/extension-list-item';
import { Placeholder } from '@tiptap/extension-placeholder';

import { RichTextToolbar } from './RichTextToolbar';
import { cn } from '@/lib/utils';

// 检查内容是否为空
const isEmptyContent = (content: any): boolean => {
  if (!content) return true;

  // 如果是字符串，检查是否为空或只包含空白字符
  if (typeof content === 'string') {
    const trimmed = content.trim();
    if (!trimmed) return true;

    // 检查是否是空的 JSON 结构
    if (trimmed.startsWith('{')) {
      try {
        const parsed = JSON.parse(trimmed);
        return isEmptyContent(parsed);
      } catch {
        return false; // 如果不是有效 JSON，认为有内容
      }
    }
    return false;
  }

  // 如果是对象，检查是否是空的 Tiptap 文档
  if (typeof content === 'object' && content.type === 'doc') {
    if (!content.content || content.content.length === 0) return true;

    // 检查是否只包含空段落
    return content.content.every((node: any) => {
      if (node.type === 'paragraph') {
        return !node.content || node.content.length === 0;
      }
      return false;
    });
  }

  return false;
};

// 解析内容格式 - 处理从后端获取的 JSON 字符串
const parseContent = (rawContent: string) => {
  if (!rawContent) {
    return {
      type: 'doc',
      content: []
    };
  }

  // 如果是 JSON 字符串，尝试解析
  if (typeof rawContent === 'string' && rawContent.startsWith('{')) {
    try {
      const parsed = JSON.parse(rawContent);
      // 如果解析后的内容为空，返回空文档
      if (isEmptyContent(parsed)) {
        return {
          type: 'doc',
          content: []
        };
      }
      return parsed;
    } catch (error) {
      console.warn('Failed to parse content as JSON:', error);
      // 如果解析失败，当作 HTML 处理
      return rawContent;
    }
  }

  // 如果已经是对象，检查是否为空
  if (typeof rawContent === 'object') {
    if (isEmptyContent(rawContent)) {
      return {
        type: 'doc',
        content: []
      };
    }
    return rawContent;
  }

  // 其他情况当作 HTML 或纯文本处理
  return rawContent;
};

export interface RichTextEditorProps {
  content?: string;
  onChange?: (content: string) => void;
  onSave?: () => void;
  placeholder?: string;
  className?: string;
  editable?: boolean;
  autoSave?: boolean;
  autoSaveDelay?: number;
}

export function RichTextEditor({
  content = '',
  onChange,
  onSave,
  placeholder = 'Start writing...',
  className,
  editable = true,
  autoSave = false,
  autoSaveDelay = 2000,
}: RichTextEditorProps) {
  const editor = useEditor({
    immediatelyRender: false,
    extensions: [
      StarterKit.configure({
        bulletList: false,
        orderedList: false,
        listItem: false,
        link: false, // 禁用默认Link扩展，使用自定义配置
      }),
      BulletList.configure({
        HTMLAttributes: {
          class: 'rich-text-bullet-list',
        },
      }),
      OrderedList.configure({
        HTMLAttributes: {
          class: 'rich-text-ordered-list',
        },
      }),
      ListItem,
      Image.configure({
        HTMLAttributes: {
          class: 'rich-text-image',
        },
      }),
      Link.configure({
        openOnClick: false,
        HTMLAttributes: {
          class: 'rich-text-link',
        },
      }),
      TextAlign.configure({
        types: ['heading', 'paragraph'],
      }),
      TextStyle,
      Color,
      Placeholder.configure({
        placeholder,
        emptyEditorClass: 'is-editor-empty',
      }),
    ],
    content: parseContent(content),
    editable,
    onUpdate: ({ editor }) => {
      const json = editor.getJSON();
      onChange?.(JSON.stringify(json));
    },
    editorProps: {
      attributes: {
        class: cn(
          'prose prose-sm sm:prose lg:prose-lg xl:prose-2xl mx-auto focus:outline-none',
          'min-h-[200px] p-4 border border-gray-200 rounded-md',
          className
        ),
      },
    },
  });

  // 当外部 content 变化时，更新编辑器内容
  useEffect(() => {
    if (!editor) return;

    const parsedContent = parseContent(content);
    const currentContent = editor.getJSON();

    // 只有当内容真正不同时才更新，避免无限循环
    if (JSON.stringify(parsedContent) !== JSON.stringify(currentContent)) {
      editor.commands.setContent(parsedContent, { emitUpdate: false });
    }
  }, [content, editor]);

  // Auto-save functionality
  useEffect(() => {
    if (!autoSave || !editor || !onSave) return;

    const timer = setTimeout(() => {
      onSave();
    }, autoSaveDelay);

    return () => clearTimeout(timer);
  }, [content, autoSave, autoSaveDelay, onSave, editor]);





  const handleSave = useCallback(() => {
    onSave?.();
  }, [onSave]);

  if (!editor) {
    return (
      <div className="animate-pulse">
        <div className="h-12 bg-gray-200 rounded mb-4"></div>
        <div className="h-48 bg-gray-200 rounded"></div>
      </div>
    );
  }

  return (
    <div className="rich-text-editor">
      <RichTextToolbar
        editor={editor}
        onSave={onSave ? handleSave : undefined}
      />
      <EditorContent
        editor={editor}
        className={cn(
          'rich-text-content',
          !editable && 'pointer-events-none opacity-75'
        )}
      />
    </div>
  );
}
