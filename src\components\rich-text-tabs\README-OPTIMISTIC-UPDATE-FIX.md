# 乐观更新修复说明

## 🐛 问题分析

乐观更新没有工作的根本原因是**查询键格式不匹配**。

### 问题详情

1. **错误的查询键格式**：
   ```typescript
   // ❌ 错误：我们使用的格式
   ['getRichtexttabsConfigsEntityTypeLanguageCodeAll', { pathParams: { entityType, languageCode } }]
   ```

2. **正确的查询键格式**：
   ```typescript
   // ✅ 正确：API 生成的格式
   ["rich-text-tabs", "configs", entityType, languageCode, "all"]
   ```

### 查询键生成逻辑

根据 `queryKeyFn` 的实现：
```typescript
export const queryKeyFn = (operation: QueryOperation): QueryKey => {
  const queryKey: unknown[] = hasPathParams(operation)
    ? operation.path
        .split("/")
        .filter(Boolean)
        .map((i) => resolvePathParam(i, operation.variables.pathParams))
    : operation.path.split("/").filter(Boolean);
  // ...
};
```

对于路径 `/rich-text-tabs/configs/{entityType}/{languageCode}/all`：
- 分割路径：`["rich-text-tabs", "configs", "{entityType}", "{languageCode}", "all"]`
- 替换参数：`["rich-text-tabs", "configs", "event", "zh", "all"]`

## 🔧 修复方案

### 1. **修复乐观更新的查询键**

**创建配置**：
```typescript
// 修复前
queryClient.setQueryData(
  ['getRichtexttabsConfigsEntityTypeLanguageCodeAll', { pathParams: { entityType, languageCode } }],
  (oldData) => [...oldData, newConfig]
);

// 修复后
queryClient.setQueryData(
  ["rich-text-tabs", "configs", entityType, currentLanguage, "all"],
  (oldData) => [...oldData, newConfig]
);
```

**更新配置**：
```typescript
// 修复后
queryClient.setQueryData(
  ["rich-text-tabs", "configs", entityType, currentLanguage, "all"],
  (oldData) => oldData.map(item => 
    item.id === updatedConfig.id ? updatedConfig : item
  )
);
```

**删除配置**：
```typescript
// 修复后
queryClient.setQueryData(
  ["rich-text-tabs", "configs", entityType, currentLanguage, "all"],
  (oldData) => oldData.filter(item => 
    item.id !== deletedId
  )
);
```

### 2. **修复缓存失效的查询键**

```typescript
// 修复前
queryClient.invalidateQueries({ 
  queryKey: ['getRichtexttabsConfigsEntityTypeLanguageCodeAll'] 
});

// 修复后
queryClient.invalidateQueries({ 
  queryKey: ["rich-text-tabs", "configs", entityType, currentLanguage, "all"] 
});
```

### 3. **查询键映射表**

| API 路径 | 查询键格式 |
|---------|-----------|
| `/rich-text-tabs/configs/{entityType}/{languageCode}` | `["rich-text-tabs", "configs", entityType, languageCode]` |
| `/rich-text-tabs/configs/{entityType}/{languageCode}/all` | `["rich-text-tabs", "configs", entityType, languageCode, "all"]` |
| `/rich-text-tabs/tabs/{entityType}/{entityId}/{languageCode}` | `["rich-text-tabs", "tabs", entityType, entityId, languageCode]` |

## 🎯 修复效果

### 立即生效的操作

1. **✅ 新建标签页**：
   - 点击"创建"后立即在配置列表中显示
   - 立即可以在标签页导航中看到
   - 无需等待 API 响应

2. **✅ 编辑标签页**：
   - 修改 label 后立即更新显示
   - 启用/禁用状态立即生效
   - 其他属性修改立即反映

3. **✅ 删除标签页**：
   - 点击删除后立即从列表消失
   - 标签页导航立即更新
   - 自动切换到其他标签页

### 数据一致性保障

1. **乐观更新**：立即更新 UI，提供即时反馈
2. **后台同步**：API 请求在后台执行
3. **缓存失效**：确保相关查询数据保持一致
4. **错误回滚**：API 失败时自动恢复（如果实现了 onError 回滚）

## 🔍 调试方法

### 1. **检查查询键**
```typescript
// 在浏览器控制台中检查当前缓存的查询键
const queryClient = useQueryClient();
const cache = queryClient.getQueryCache();
const queries = cache.getAll();
queries.forEach(query => {
  console.log('查询键:', query.queryKey);
});
```

### 2. **验证缓存更新**
```typescript
// 检查特定查询的缓存数据
const data = queryClient.getQueryData([
  "rich-text-tabs", "configs", "event", "zh", "all"
]);
console.log('缓存数据:', data);
```

### 3. **监听缓存变化**
```typescript
// 监听查询缓存的变化
queryClient.getQueryCache().subscribe((event) => {
  if (event.type === 'updated') {
    console.log('缓存更新:', event.query.queryKey, event.query.state.data);
  }
});
```

## 🎉 最终效果

现在用户在使用富文本标签页配置管理时会体验到：

- ⚡ **即时响应**：所有操作立即在 UI 中反映
- 🔄 **无缝体验**：不再需要等待或手动刷新
- 🎯 **精确更新**：只更新相关的 UI 部分
- 🛡️ **数据一致性**：后台确保数据同步

**乐观更新现在完全正常工作！** 🎊
