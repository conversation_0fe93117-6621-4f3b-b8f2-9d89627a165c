# 富文本标签页配置管理功能

## 🎉 新功能概述

现在 `RichTextTabsManager` 组件支持完整的标签页配置管理功能，包括：

- ✅ **修改标签页 label**
- ✅ **新增标签页**
- ✅ **删除标签页**（预设标签页受保护）
- ✅ **编辑标签页属性**（placeholder、图标、排序等）
- ✅ **启用/禁用标签页**

## 🚀 使用方法

### 1. 启用配置管理模式

在 `RichTextTabsManager` 组件中，点击右上角的 **"配置管理"** 按钮即可启用管理模式。

```tsx
// 组件会自动显示配置管理按钮
<RichTextTabsManager
  entityType="event"
  entityId="your-event-id"
  defaultLanguage="zh"
/>
```

### 2. 管理功能说明

#### 📝 新建标签页
1. 点击 **"配置管理"** 按钮
2. 点击 **"新建标签页"** 按钮
3. 填写标签页信息：
   - **标签名称**：显示给用户的名称（必填）
   - **占位符文本**：编辑器的提示文本
   - **图标**：可选的图标名称
   - **排序**：显示顺序
   - **启用状态**：是否显示该标签页
   - **预设标签页**：是否为系统预设（预设标签页不能删除）
   - **键名**：由系统自动生成，无需手动输入

#### ✏️ 编辑标签页
1. 在配置管理面板中，点击标签页右侧的 **编辑** 按钮
2. 修改可编辑的属性（键名不可修改）
3. 点击 **"保存"** 完成修改

#### 🗑️ 删除标签页
1. 在配置管理面板中，点击标签页右侧的 **删除** 按钮
2. 确认删除操作
3. **注意**：预设标签页不能删除

## 🔧 API 支持

新功能使用以下 API 接口：

### 配置管理接口
```
POST   /rich-text-tabs/configs           # 创建新配置
PUT    /rich-text-tabs/configs/{id}      # 更新配置
DELETE /rich-text-tabs/configs/{id}      # 删除配置
POST   /rich-text-tabs/configs/reorder   # 重新排序
```

### Hook 新增方法
```typescript
const {
  // 原有功能...
  
  // 新增的配置管理功能
  createConfig,      // 创建新标签页配置
  updateConfig,      // 更新标签页配置
  deleteConfig,      // 删除标签页配置
  reorderConfigs,    // 重新排序标签页
  isConfigMutating,  // 配置操作加载状态
} = useRichTextTabs({
  entityType: 'event',
  entityId: 'your-id',
  languageCode: 'zh',
  isAdminMode: true, // 启用管理模式
});
```

## 📋 配置字段说明

### TabConfig 接口
```typescript
interface TabConfig {
  id: string;                    // 唯一标识符
  entity_type: "event" | "venue"; // 实体类型
  language_code: "en" | "zh" | "ja"; // 语言代码
  key: string;                   // 标签页键名（创建后不可修改）
  label: string;                 // 显示标签
  placeholder?: string;          // 编辑器占位符
  icon?: string;                 // 图标名称
  sort_order: number;            // 排序顺序
  is_active: boolean;            // 是否启用
  is_preset: boolean;            // 是否为预设（预设不可删除）
  deleted_at?: string;           // 删除时间
  deleted_by?: string;           // 删除者
  created_at: string;            // 创建时间
  updated_at: string;            // 更新时间
}
```

## 🛡️ 权限和限制

### 预设标签页保护
- 预设标签页（`is_preset: true`）不能被删除
- 预设标签页的某些属性可能受到限制
- 系统会显示 **"预设"** 标识

### 数据验证
- **键名**：必须符合 `^[a-zA-Z0-9_-]+$` 格式
- **标签名称**：不能为空，最大 100 字符
- **占位符**：最大 200 字符
- **图标**：最大 50 字符

## 🎨 UI 特性

### 视觉反馈
- **未保存更改**：橙色圆点提示
- **预设标签页**：灰色 "预设" 标识
- **禁用状态**："已禁用" 标识
- **加载状态**：按钮显示加载动画

### 响应式设计
- 配置管理面板自适应布局
- 移动端友好的操作按钮
- 清晰的视觉层次

## 🔄 数据同步

### 自动刷新
- 创建/更新/删除操作后自动刷新数据
- 使用 React Query 进行缓存管理
- 乐观更新提升用户体验

### 错误处理
- 完整的错误提示和处理
- 操作失败时的回滚机制
- 用户友好的错误信息

## 📱 使用示例

### 完整的管理界面
```tsx
import { RichTextTabsManager } from '@/components/rich-text-tabs';

export function EventEditPage({ eventId }: { eventId: string }) {
  return (
    <div className="space-y-6">
      <h1>编辑活动</h1>
      
      {/* 富文本标签页管理 - 包含完整的配置管理功能 */}
      <RichTextTabsManager
        entityType="event"
        entityId={eventId}
        defaultLanguage="zh"
      />
    </div>
  );
}
```

### 只读模式（无配置管理）
```tsx
import { RichTextTabsViewer } from '@/components/rich-text-tabs';

export function EventDetailPage({ eventId }: { eventId: string }) {
  return (
    <div className="space-y-6">
      <h1>活动详情</h1>
      
      {/* 只读模式 - 不显示配置管理功能 */}
      <RichTextTabsViewer
        entityType="event"
        entityId={eventId}
        defaultLanguage="zh"
      />
    </div>
  );
}
```

## 🎯 最佳实践

1. **权限控制**：确保只有管理员能访问配置管理功能
2. **数据备份**：重要操作前建议备份数据
3. **测试验证**：新建标签页后测试内容编辑功能
4. **用户培训**：为内容管理员提供使用指导

---

**🎉 现在你可以完全自定义富文本标签页的结构和内容了！**
