/**
 * 管理员图片管理页面
 * 提供完整的图片管理功能
 */

'use client';

import React, { useState } from 'react';
import { Search, Filter, Settings } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { ImageManager } from '@/components/images';
import BetterAuthRoleGuard from '@/components/BetterAuthRoleGuard';
import type { ImageCategory } from '@/types/image';

export default function AdminImagesPage() {
  const [selectedCategory, setSelectedCategory] = useState<ImageCategory>('event');
  const [selectedResourceId, setSelectedResourceId] = useState<string>('');
  const [searchQuery, setSearchQuery] = useState('');

  // 示例资源ID列表（实际应用中应该从API获取）
  const resourceIds = {
    event: ['comiket-103', 'reitaisai-22', 'comic-city-osaka-157'],
    circle: ['circle-001', 'circle-002', 'circle-003'],
    venue: ['tokyo-big-sight', 'makuhari-messe', 'pacifico-yokohama'],
  };

  const handleCategoryChange = (category: ImageCategory) => {
    setSelectedCategory(category);
    setSelectedResourceId(''); // 重置资源ID选择
  };

  const currentResourceIds = resourceIds[selectedCategory] || [];

  return (
    <BetterAuthRoleGuard allow={['admin', 'editor']}>
      <div className="container mx-auto py-8 space-y-8">
        {/* 页面头部 */}
        <div className="space-y-2">
          <h1 className="text-3xl font-bold">图片管理</h1>
          <p className="text-muted-foreground">
            上传、整理和管理展会、社团和场馆的图片资源。
          </p>
        </div>

        {/* 筛选控制 */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              筛选条件
            </CardTitle>
            <CardDescription>
              选择分类和资源来管理图片
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* 分类选择 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">分类</label>
                <Select
                  value={selectedCategory}
                  onValueChange={handleCategoryChange}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择分类" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="event">展会</SelectItem>
                    <SelectItem value="circle">社团</SelectItem>
                    <SelectItem value="venue">场馆</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* 资源ID选择 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">资源ID</label>
                <Select
                  value={selectedResourceId}
                  onValueChange={setSelectedResourceId}
                  disabled={!selectedCategory}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="选择资源" />
                  </SelectTrigger>
                  <SelectContent>
                    {currentResourceIds.map((id) => (
                      <SelectItem key={id} value={id}>
                        {id}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* 搜索 */}
              <div className="space-y-2">
                <label className="text-sm font-medium">搜索</label>
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="搜索图片..."
                    value={searchQuery}
                    onChange={(e) => setSearchQuery(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
            </div>

            {/* 快速操作 */}
            <div className="flex items-center gap-2 pt-4 border-t">
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                设置
              </Button>
              <div className="text-sm text-muted-foreground">
                快速操作和批量处理功能将在此显示
              </div>
            </div>
          </CardContent>
        </Card>

        {/* 图片管理器 */}
        {selectedCategory && selectedResourceId ? (
          <ImageManager
            category={selectedCategory}
            resourceId={selectedResourceId}
            allowUpload={true}
            allowDelete={true}
            showFilters={true}
          />
        ) : (
          <Card>
            <CardContent className="py-12">
              <div className="text-center space-y-4">
                <div className="text-6xl">📷</div>
                <div>
                  <h3 className="text-lg font-medium">选择分类和资源</h3>
                  <p className="text-muted-foreground">
                    选择一个分类和资源ID来开始管理图片
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* 使用说明 */}
        <Card>
          <CardHeader>
            <CardTitle>使用指南</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <h4 className="font-medium mb-2">支持格式</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• JPEG (.jpg, .jpeg)</li>
                  <li>• PNG (.png)</li>
                  <li>• WebP (.webp)</li>
                  <li>• GIF (.gif)</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">文件要求</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• 最小尺寸：100字节</li>
                  <li>• 最大尺寸：10MB</li>
                  <li>• 建议：高质量图片</li>
                  <li>• 支持多文件上传</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">图片类型</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <strong>海报 (Poster)：</strong>主要宣传图片</li>
                  <li>• <strong>标志 (Logo)：</strong>品牌和标识图片</li>
                  <li>• <strong>横幅 (Banner)：</strong>头部和封面图片</li>
                  <li>• <strong>画廊 (Gallery)：</strong>通用照片集合</li>
                </ul>
              </div>
              <div>
                <h4 className="font-medium mb-2">图片变体</h4>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• <strong>原图 (Original)：</strong>完整分辨率</li>
                  <li>• <strong>大图 (Large)：</strong>高质量显示</li>
                  <li>• <strong>中图 (Medium)：</strong>标准显示</li>
                  <li>• <strong>缩略图 (Thumb)：</strong>缩略图预览</li>
                </ul>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </BetterAuthRoleGuard>
  );
}
