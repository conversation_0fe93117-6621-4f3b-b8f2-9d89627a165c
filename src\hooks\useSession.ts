/**
 * Better Auth Session Hook
 * 使用官方 Better Auth React 客户端的 session 管理
 */

import { useSession as useBetterAuthSession } from '@/lib/auth-client';
import type { User } from '@/types/user';

/**
 * 使用 Better Auth 的 session hook
 * 提供类型安全的用户 session 管理
 */
export function useSession() {
  const session = useBetterAuthSession();

  // 转换 Better Auth 用户数据为我们的 User 类型
  const user: User | null = session.data?.user ? {
    id: session.data.user.id,
    email: session.data.user.email,
    name: session.data.user.name,
    role: (session.data.user as any).role || 'user', // 后端返回 role 字段
    emailVerified: session.data.user.emailVerified,
    createdAt: session.data.user.createdAt.toISOString(),
    updatedAt: session.data.user.updatedAt.toISOString(),
  } : null;

  return {
    user,
    session: session.data?.session || null,
    isLoading: session.isPending,
    error: session.error,
    isAuthenticated: !!session.data?.user,
  };
}

/**
 * 使用示例：
 *
 * ```tsx
 * function UserProfile() {
 *   const { user, isLoading, isAuthenticated } = useSession();
 *
 *   if (isLoading) return <div>Loading...</div>;
 *   if (!isAuthenticated) return <div>Please login</div>;
 *
 *   return (
 *     <div>
 *       <h1>Welcome, {user?.name}</h1>
 *       <p>Email: {user?.email}</p>
 *       <p>Role: {user?.role}</p>
 *     </div>
 *   );
 * }
 * ```
 */
