"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter, useSearchParams } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { RadixButton, RadixCard, RadixCardHeader, RadixCardTitle, RadixCardDescription, RadixCardContent, RadixInput, RadixLabel } from "@/components/ui/radix-components";
import { useAuth } from "@/contexts/user";
import { showApiError } from "@/lib/show-error";

// 登录表单校验 schema
const loginSchema = z.object({
  identifier: z.string().min(1, "请输入邮箱地址或用户名"),
  password: z.string().min(6, "密码至少需要6个字符"),
});

type LoginFormValues = z.infer<typeof loginSchema>;

export default function LoginPage() {
  const router = useRouter();
  const searchParams = useSearchParams();
  const { login } = useAuth();

  // 获取重定向URL，默认为首页
  const redirectUrl = searchParams.get('redirect') || '/';
  const form = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      identifier: "",
      password: "",
    },
  });

  const {
    handleSubmit,
    register,
    formState: { errors, isSubmitting },
  } = form;

  const onSubmit = async (data: LoginFormValues) => {
    try {
      await login(data.identifier, data.password);
      // 登录成功后跳转到重定向URL或首页
      router.push(redirectUrl);
    } catch (error) {
      showApiError(error, "登录失败");
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-background">
      <RadixCard className="w-full max-w-sm">
        <RadixCardHeader>
          <RadixCardTitle className="text-2xl">登录</RadixCardTitle>
          <RadixCardDescription>请输入您的邮箱地址或用户名和密码</RadixCardDescription>
        </RadixCardHeader>
        <RadixCardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="grid gap-4" autoComplete="on">
            <div className="grid gap-2">
              <RadixLabel htmlFor="identifier">邮箱地址或用户名</RadixLabel>
              <RadixInput
                id="identifier"
                type="text"
                placeholder="<EMAIL> 或 username"
                autoComplete="email username"
                {...register("identifier")}
                disabled={isSubmitting}
              />
              {errors.identifier && <p className="text-xs text-destructive">{errors.identifier.message}</p>}
            </div>
            <div className="grid gap-2">
              <RadixLabel htmlFor="password">密码</RadixLabel>
              <RadixInput
                id="password"
                type="password"
                placeholder="Password"
                autoComplete="current-password"
                {...register("password")}
                disabled={isSubmitting}
              />
              {errors.password && <p className="text-xs text-destructive">{errors.password.message}</p>}
            </div>
            <RadixButton type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? "登录中..." : "登录"}
            </RadixButton>
          </form>
        </RadixCardContent>
      </RadixCard>
    </div>
  );
} 