/**
 * 面包屑导航组件 - 射命丸文主题
 * 提供清晰的页面层次导航，增强用户体验
 */

import * as React from "react";
import { ChevronRight, Home } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import { cn } from "@/lib/utils";

export interface BreadcrumbItem {
  label: string;
  href: string;
  icon?: React.ComponentType<{ className?: string }>;
}

export interface BreadcrumbProps {
  items?: BreadcrumbItem[];
  className?: string;
  showHome?: boolean;
  separator?: React.ReactNode;
}

/**
 * 智能面包屑导航组件
 * 自动生成或使用提供的导航项
 */
export function Breadcrumb({
  items,
  className,
  showHome = true,
  separator = <ChevronRight className="h-4 w-4" />,
}: BreadcrumbProps) {
  const pathname = usePathname();
  const breadcrumbItems = items || generateBreadcrumbs(pathname);

  if (breadcrumbItems.length === 0) {
    return null;
  }

  return (
    <nav 
      className={cn("flex items-center space-x-2 text-sm text-muted-foreground mb-6", className)}
      aria-label="面包屑导航"
    >
      {/* 首页链接 */}
      {showHome && (
        <>
          <BreadcrumbLink href="/" className="flex items-center gap-1 hover:text-primary">
            <Home className="h-4 w-4" />
            <span className="sr-only">首页</span>
          </BreadcrumbLink>
          {breadcrumbItems.length > 0 && (
            <span className="text-muted-foreground/50">{separator}</span>
          )}
        </>
      )}

      {/* 面包屑项 */}
      {breadcrumbItems.map((item, index) => (
        <React.Fragment key={item.href}>
          {index > 0 && (
            <span className="text-muted-foreground/50">{separator}</span>
          )}
          
          {index === breadcrumbItems.length - 1 ? (
            // 当前页面 - 不可点击
            <span className={cn(
              "text-foreground font-medium flex items-center gap-1",
              "relative after:absolute after:bottom-0 after:left-0 after:w-full after:h-0.5 after:bg-primary after:rounded-full"
            )}>
              {item.icon && <item.icon className="h-4 w-4" />}
              {item.label}
            </span>
          ) : (
            // 可点击的面包屑项
            <BreadcrumbLink 
              href={item.href}
              className="flex items-center gap-1 hover:text-primary transition-colors"
            >
              {item.icon && <item.icon className="h-4 w-4" />}
              {item.label}
            </BreadcrumbLink>
          )}
        </React.Fragment>
      ))}
    </nav>
  );
}

/**
 * 面包屑链接组件
 */
function BreadcrumbLink({
  href,
  children,
  className,
}: {
  href: string;
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <Link
      href={href}
      className={cn(
        "transition-all duration-200 relative group",
        "hover:text-primary focus:outline-none focus:text-primary",
        className
      )}
    >
      {children}
      {/* 悬停下划线效果 */}
      <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary rounded-full transition-all duration-300 group-hover:w-full" />
    </Link>
  );
}

/**
 * 自动生成面包屑导航项
 * 基于当前路径生成导航层次
 */
function generateBreadcrumbs(pathname: string): BreadcrumbItem[] {
  // 移除开头的斜杠并分割路径
  const pathSegments = pathname.split('/').filter(Boolean);
  
  if (pathSegments.length === 0) {
    return [];
  }

  const breadcrumbs: BreadcrumbItem[] = [];
  let currentPath = '';

  // 路径映射配置
  const pathLabels: Record<string, string> = {
    'events': '展会',
    'circles': '社团',
    'feed': '动态',
    'news': '新闻',
    'admin': '管理后台',
    'profile': '个人资料',
    'settings': '设置',
    'bookmarks': '收藏',
    'search': '搜索',
    'about': '关于',
    'help': '帮助',
    'contact': '联系我们',
  };

  pathSegments.forEach((segment, index) => {
    currentPath += `/${segment}`;
    
    // 获取显示标签
    const label = pathLabels[segment] || formatSegment(segment);
    
    breadcrumbs.push({
      label,
      href: currentPath,
    });
  });

  return breadcrumbs;
}

/**
 * 格式化路径段为显示文本
 */
function formatSegment(segment: string): string {
  // 如果是数字ID，显示为"详情"
  if (/^\d+$/.test(segment)) {
    return '详情';
  }
  
  // 移除连字符和下划线，首字母大写
  return segment
    .replace(/[-_]/g, ' ')
    .replace(/\b\w/g, l => l.toUpperCase());
}

/**
 * 紧凑型面包屑组件
 * 适用于移动端或空间受限的场景
 */
export function CompactBreadcrumb({
  items,
  className,
}: Pick<BreadcrumbProps, 'items' | 'className'>) {
  const pathname = usePathname();
  const breadcrumbItems = items || generateBreadcrumbs(pathname);

  if (breadcrumbItems.length === 0) {
    return null;
  }

  const currentItem = breadcrumbItems[breadcrumbItems.length - 1];
  const parentItem = breadcrumbItems[breadcrumbItems.length - 2];

  return (
    <nav className={cn("flex items-center text-sm text-muted-foreground", className)}>
      {parentItem && (
        <>
          <BreadcrumbLink 
            href={parentItem.href}
            className="hover:text-primary transition-colors"
          >
            {parentItem.label}
          </BreadcrumbLink>
          <ChevronRight className="h-4 w-4 mx-1" />
        </>
      )}
      <span className="text-foreground font-medium">{currentItem.label}</span>
    </nav>
  );
}

/**
 * 带返回按钮的面包屑组件
 */
export function BreadcrumbWithBack({
  items,
  className,
  onBack,
}: BreadcrumbProps & {
  onBack?: () => void;
}) {
  const pathname = usePathname();
  const breadcrumbItems = items || generateBreadcrumbs(pathname);

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else if (typeof window !== 'undefined') {
      window.history.back();
    }
  };

  return (
    <div className={cn("flex items-center gap-4", className)}>
      {/* 返回按钮 */}
      <button
        onClick={handleBack}
        className="flex items-center gap-2 text-sm text-muted-foreground hover:text-primary transition-colors p-2 rounded-lg hover:bg-primary/10"
        aria-label="返回上一页"
      >
        <ChevronRight className="h-4 w-4 rotate-180" />
        <span>返回</span>
      </button>

      {/* 分隔线 */}
      <div className="w-px h-4 bg-border" />

      {/* 面包屑导航 */}
      <Breadcrumb items={breadcrumbItems} showHome={false} />
    </div>
  );
}

export default Breadcrumb;
