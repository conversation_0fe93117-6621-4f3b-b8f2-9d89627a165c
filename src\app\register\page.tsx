"use client";

import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { z } from "zod";

import { <PERSON>dixButton, RadixCard, RadixCardHeader, RadixCardTitle, RadixCardDescription, RadixCardContent, RadixCardFooter, RadixInput, RadixLabel } from "@/components/ui/radix-components";
import { useAuth } from "@/contexts/user";
import { showApiError } from "@/lib/show-error";
import { toast } from "sonner";


// 注册表单校验 schema
const registerSchema = z
  .object({
    email: z.string().email("请输入有效的邮箱地址"),
    name: z.string().min(1, "姓名不能为空").optional(),
    username: z.string().min(3, "用户名至少需要3个字符").optional(),
    password: z.string().min(6, "密码至少需要6个字符"),
    confirmPassword: z.string(),
  })
  .refine((data) => data.password === data.confirmPassword, {
    message: "两次输入的密码不一致",
    path: ["confirmPassword"],
  });

type RegisterFormValues = z.infer<typeof registerSchema>;

export default function RegisterPage() {
  const router = useRouter();
  const { register: signup } = useAuth();
  const form = useForm<RegisterFormValues>({
    resolver: zodResolver(registerSchema),
    defaultValues: {
      email: "",
      name: "",
      username: "",
      password: "",
      confirmPassword: "",
    },
  });

  const {
    handleSubmit,
    register,
    formState: { errors, isSubmitting },
  } = form;

  const onSubmit = async (data: RegisterFormValues) => {
    try {
      await signup({
        email: data.email,
        password: data.password,
        name: data.name,
        username: data.username
      });
      toast.success("注册成功！", {
        description: "现在您可以使用新账户登录了。",
      });
      router.push("/login");
    } catch (error) {
      showApiError(error, "注册失败");
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-background">
      <RadixCard className="w-full max-w-sm">
        <RadixCardHeader>
          <RadixCardTitle className="text-2xl">注册新账户</RadixCardTitle>
          <RadixCardDescription>创建一个账户以继续</RadixCardDescription>
        </RadixCardHeader>
        <RadixCardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="grid gap-4" autoComplete="on">
            <div className="grid gap-2">
              <RadixLabel htmlFor="email">邮箱地址</RadixLabel>
              <RadixInput
                id="email"
                type="email"
                placeholder="<EMAIL>"
                autoComplete="email"
                {...register("email")}
                disabled={isSubmitting}
              />
              {errors.email && <p className="text-xs text-destructive">{errors.email.message}</p>}
            </div>
            <div className="grid gap-2">
              <RadixLabel htmlFor="name">姓名 (可选)</RadixLabel>
              <RadixInput
                id="name"
                type="text"
                placeholder="Your Name"
                autoComplete="name"
                {...register("name")}
                disabled={isSubmitting}
              />
              {errors.name && <p className="text-xs text-destructive">{errors.name.message}</p>}
            </div>
            <div className="grid gap-2">
              <RadixLabel htmlFor="username">用户名 (可选)</RadixLabel>
              <RadixInput
                id="username"
                type="text"
                placeholder="username"
                autoComplete="username"
                {...register("username")}
                disabled={isSubmitting}
              />
              {errors.username && <p className="text-xs text-destructive">{errors.username.message}</p>}
            </div>
            <div className="grid gap-2">
              <RadixLabel htmlFor="password">密码</RadixLabel>
              <RadixInput
                id="password"
                type="password"
                placeholder="Password"
                autoComplete="new-password"
                {...register("password")}
                disabled={isSubmitting}
              />
              {errors.password && <p className="text-xs text-destructive">{errors.password.message}</p>}
            </div>
            <div className="grid gap-2">
              <RadixLabel htmlFor="confirmPassword">确认密码</RadixLabel>
              <RadixInput
                id="confirmPassword"
                type="password"
                placeholder="Confirm Password"
                autoComplete="new-password"
                {...register("confirmPassword")}
                disabled={isSubmitting}
              />
              {errors.confirmPassword && (
                <p className="text-xs text-destructive">{errors.confirmPassword.message}</p>
              )}
            </div>
            <RadixButton type="submit" className="w-full" disabled={isSubmitting}>
              {isSubmitting ? "注册中..." : "创建账户"}
            </RadixButton>
          </form>
        </RadixCardContent>
        <RadixCardFooter className="justify-center">
          <p className="text-sm text-muted-foreground">
            已经有账户了？{" "}
            <Link href="/login" className="font-medium text-primary hover:underline">
              去登录
            </Link>
          </p>
        </RadixCardFooter>
      </RadixCard>
    </div>
  );
} 