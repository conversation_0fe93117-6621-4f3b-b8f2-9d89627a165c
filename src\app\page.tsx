"use client";

import { useTranslations } from 'next-intl'
import Link from 'next/link'
import { motion } from 'motion/react'

import EventHero from '@/components/home/<USER>'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { useGetEvents } from '@/api/generated/ayafeedComponents'
import { EventImageDisplay } from '@/components/events/EventImageDisplay'
import { ModernPageLayout, Container, GradientHeading } from '@/components/design-system'

export default function Home() {
  const t = useTranslations('HomePage')

  // 获取更多展会数据用于预览
  const { data: upcomingEvents } = useGetEvents({
    queryParams: {
      page: '1',
      pageSize: '8'
    }
  })

  const { data: pastEvents } = useGetEvents({
    queryParams: {
      page: '1',
      pageSize: '8'
    }
  })

  return (
    <ModernPageLayout>
      {/* Hero Section */}
      <section className="relative">
        <EventHero />
      </section>

      {/* 即将举办的展会 */}
      <motion.section
        className="relative py-16"
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-100px" }}
        transition={{ duration: 0.6 }}
      >
        <Container>
          <motion.div
            className="flex items-center justify-between mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <GradientHeading level={2} gradient="brand">
              {t('upcomingEvents')}
            </GradientHeading>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button asChild variant="outline">
                <Link href="/events">{t('viewDetails')}</Link>
              </Button>
            </motion.div>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            {upcomingEvents?.items?.slice(0, 4).map((event, index) => (
              <motion.div
                key={event.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.1 * index, duration: 0.5 }}
              >
                <EventPreviewCard event={event} />
              </motion.div>
            ))}
          </motion.div>
        </Container>
      </motion.section>

      {/* 往期展会 */}
      <motion.section
        className="relative py-16 bg-muted/30 backdrop-blur-sm"
        initial={{ opacity: 0, y: 50 }}
        whileInView={{ opacity: 1, y: 0 }}
        viewport={{ once: true, margin: "-100px" }}
        transition={{ duration: 0.6 }}
      >
        <Container>
          <motion.div
            className="flex items-center justify-between mb-8"
            initial={{ opacity: 0, y: 20 }}
            whileInView={{ opacity: 1, y: 0 }}
            viewport={{ once: true }}
            transition={{ delay: 0.2, duration: 0.5 }}
          >
            <GradientHeading level={2} gradient="brand">
              {t('pastEvents')}
            </GradientHeading>
            <motion.div
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Button asChild variant="outline">
                <Link href="/events">{t('viewDetails')}</Link>
              </Button>
            </motion.div>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
            initial={{ opacity: 0 }}
            whileInView={{ opacity: 1 }}
            viewport={{ once: true }}
            transition={{ delay: 0.4, duration: 0.6 }}
          >
            {pastEvents?.items?.slice(0, 4).map((event, index) => (
              <motion.div
                key={event.id}
                initial={{ opacity: 0, y: 30 }}
                whileInView={{ opacity: 1, y: 0 }}
                viewport={{ once: true }}
                transition={{ delay: 0.1 * index, duration: 0.5 }}
              >
                <EventPreviewCard event={event} />
              </motion.div>
            ))}
          </motion.div>
        </Container>
      </motion.section>
    </ModernPageLayout>
  );
}

// 展会预览卡片组件 - 射命丸文新闻主题
function EventPreviewCard({ event }: { event: any }) {
  return (
    <Link href={`/events/${event.id}`} className="group">
      <motion.div
        whileHover={{ y: -8, scale: 1.02 }}
        transition={{ duration: 0.3, ease: "easeOut" }}
      >
        <Card className="overflow-hidden transition-all duration-300 hover:shadow-2xl hover:shadow-primary/10 border border-border/50 bg-card/80 backdrop-blur-xl hover:border-primary/20 relative">
          {/* 新闻主题悬停效果 */}
          <div className="absolute inset-0 bg-gradient-to-r from-primary/0 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none rounded-xl" />

          {/* 风的装饰效果 */}
          <div className="absolute top-4 right-4 wind-decoration opacity-0 group-hover:opacity-100 transition-opacity duration-500" />

          <CardHeader className="p-0 relative">
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ duration: 0.3 }}
              className="overflow-hidden"
            >
              <EventImageDisplay
                eventId={event.id}
                imageType="poster"
                variant="medium"
                width={300}
                height={200}
                alt={event.name}
                className="w-full h-48"
                showLoading={true}
                showError={false}
              />
            </motion.div>
            {/* 悬停遮罩 */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent"
              initial={{ opacity: 0 }}
              whileHover={{ opacity: 1 }}
              transition={{ duration: 0.3 }}
            />
            {/* 新闻主题装饰性渐变边框 */}
            <motion.div
              className="absolute inset-0 bg-gradient-to-r from-primary/20 via-accent/20 to-primary/20 opacity-0 group-hover:opacity-100 transition-opacity duration-300"
              style={{ mixBlendMode: 'overlay' }}
            />
          </CardHeader>
          <CardContent className="p-4 relative z-10">
            <motion.div
              initial={{ opacity: 0, y: 10 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.1, duration: 0.3 }}
            >
              <CardTitle className="text-lg font-semibold line-clamp-2 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent group-hover:from-primary group-hover:to-primary/70 transition-all duration-300">
                {event.name}
              </CardTitle>
              <motion.p
                className="text-sm text-muted-foreground mt-2"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ delay: 0.2, duration: 0.3 }}
              >
                {event.date}
              </motion.p>
              {event.venue_name && (
                <motion.p
                  className="text-sm text-muted-foreground/80"
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ delay: 0.3, duration: 0.3 }}
                >
                  {event.venue_name}
                </motion.p>
              )}
            </motion.div>
          </CardContent>
        </Card>
      </motion.div>
    </Link>
  )
}
