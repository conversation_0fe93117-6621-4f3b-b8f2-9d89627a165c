/**
 * 新闻卡片组件 - 射命丸文主题
 * 专为展会信息和新闻内容设计的现代化卡片组件
 */

import * as React from "react";
import { motion } from "motion/react";
import { Clock, Tag, AlertTriangle, Star } from "lucide-react";
import { cn } from "@/lib/utils";
import Image from "next/image";

export interface NewsCardProps {
  title: string;
  excerpt: string;
  publishTime: string;
  category: string;
  urgent?: boolean;
  featured?: boolean;
  imageUrl?: string;
  href?: string;
  className?: string;
  onClick?: () => void;
}

/**
 * 新闻卡片组件
 * 支持紧急、特色等不同状态，体现射命丸文的新闻记者主题
 */
export function NewsCard({
  title,
  excerpt,
  publishTime,
  category,
  urgent = false,
  featured = false,
  imageUrl,
  href,
  className,
  onClick,
}: NewsCardProps) {
  const CardComponent = href ? motion.a : motion.article;
  const cardProps = href ? { href } : {};

  return (
    <CardComponent
      className={cn(
        "group relative overflow-hidden rounded-xl border bg-card transition-all duration-300 cursor-pointer",
        "hover:shadow-lg hover:-translate-y-1 focus:outline-none focus:ring-2 focus:ring-primary/20",
        urgent && "border-l-4 border-l-primary bg-primary/5",
        featured && "bg-gradient-to-br from-primary/5 to-accent/5 border-primary/20",
        className
      )}
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
      transition={{ duration: 0.2 }}
      onClick={onClick}
      {...cardProps}
    >
      {/* 紧急新闻标识 */}
      {urgent && (
        <div className="absolute top-3 right-3 bg-primary text-primary-foreground text-xs px-2 py-1 rounded-full flex items-center gap-1 z-10">
          <AlertTriangle className="h-3 w-3" />
          紧急
        </div>
      )}

      {/* 特色新闻标识 */}
      {featured && !urgent && (
        <div className="absolute top-3 right-3 bg-accent text-accent-foreground text-xs px-2 py-1 rounded-full flex items-center gap-1 z-10">
          <Star className="h-3 w-3" />
          特色
        </div>
      )}

      {/* 图片区域 */}
      {imageUrl && (
        <div className="relative h-48 overflow-hidden">
          <Image
            src={imageUrl}
            alt={title}
            width={400}
            height={200}
            className="w-full h-full object-cover transition-transform duration-300 group-hover:scale-105"
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/20 to-transparent"></div>
        </div>
      )}

      {/* 内容区域 */}
      <div className="p-6">
        {/* 分类和时间 */}
        <div className="flex items-center gap-3 mb-3">
          <div className="flex items-center gap-1">
            <Tag className="h-3 w-3 text-primary" />
            <span className="text-xs font-medium text-primary bg-primary/10 px-2 py-1 rounded">
              {category}
            </span>
          </div>
          <div className="flex items-center gap-1 text-xs text-muted-foreground">
            <Clock className="h-3 w-3" />
            <time>{publishTime}</time>
          </div>
        </div>

        {/* 标题 */}
        <h3 className="font-bold text-lg mb-2 group-hover:text-primary transition-colors line-clamp-2">
          {title}
        </h3>

        {/* 摘要 */}
        <p className="text-muted-foreground text-sm line-clamp-3 mb-4">
          {excerpt}
        </p>

        {/* 阅读更多指示器 */}
        <div className="flex items-center text-primary text-sm font-medium">
          <span className="group-hover:translate-x-1 transition-transform duration-300">
            阅读更多
          </span>
          <div className="ml-2 w-4 h-0.5 bg-primary rounded-full transform origin-left scale-x-0 group-hover:scale-x-100 transition-transform duration-300"></div>
        </div>
      </div>

      {/* 悬停效果 */}
      <div className="absolute inset-0 bg-gradient-to-r from-primary/0 to-primary/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />

      {/* 风的装饰效果 */}
      <div className="absolute top-4 left-4 wind-decoration opacity-0 group-hover:opacity-100 transition-opacity duration-500" />
    </CardComponent>
  );
}

/**
 * 紧凑型新闻卡片
 * 适用于列表视图或侧边栏
 */
export function CompactNewsCard({
  title,
  publishTime,
  category,
  urgent = false,
  href,
  className,
  onClick,
}: Pick<NewsCardProps, 'title' | 'publishTime' | 'category' | 'urgent' | 'href' | 'className' | 'onClick'>) {
  const CardComponent = href ? motion.a : motion.div;
  const cardProps = href ? { href } : {};

  return (
    <CardComponent
      className={cn(
        "group flex items-center gap-3 p-3 rounded-lg border bg-card hover:bg-muted/50 transition-all duration-200 cursor-pointer",
        urgent && "border-l-2 border-l-primary bg-primary/5",
        className
      )}
      whileHover={{ x: 4 }}
      transition={{ duration: 0.2 }}
      onClick={onClick}
      {...cardProps}
    >
      {/* 状态指示器 */}
      <div className={cn(
        "w-2 h-2 rounded-full flex-shrink-0",
        urgent ? "bg-primary animate-pulse" : "bg-muted-foreground/30"
      )} />

      {/* 内容 */}
      <div className="flex-1 min-w-0">
        <h4 className="font-medium text-sm group-hover:text-primary transition-colors line-clamp-1">
          {title}
        </h4>
        <div className="flex items-center gap-2 mt-1">
          <span className="text-xs text-primary bg-primary/10 px-1.5 py-0.5 rounded">
            {category}
          </span>
          <span className="text-xs text-muted-foreground">
            {publishTime}
          </span>
        </div>
      </div>

      {/* 箭头指示器 */}
      <div className="text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all duration-200">
        →
      </div>
    </CardComponent>
  );
}

/**
 * 新闻卡片网格容器
 * 提供响应式网格布局
 */
export function NewsCardGrid({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={cn(
      "grid gap-6",
      "grid-cols-1 md:grid-cols-2 lg:grid-cols-3",
      className
    )}>
      {children}
    </div>
  );
}

/**
 * 新闻卡片列表容器
 * 提供垂直列表布局
 */
export function NewsCardList({
  children,
  className,
}: {
  children: React.ReactNode;
  className?: string;
}) {
  return (
    <div className={cn("space-y-4", className)}>
      {children}
    </div>
  );
}

export default NewsCard;
