/**
 * 权限管理Hook
 * 基于角色的访问控制 (RBAC) 系统
 *
 * 角色定义：
 * - admin: 拥有所有权限，包括用户管理
 * - editor: 拥有除用户管理外的所有权限
 * - user: 登录用户，可收藏社团和访问个人中心
 * - viewer: 未登录用户，只能查看公共内容
 */

import { useAuth } from '@/contexts/user';
import type { UserRole } from '@/types/user';

export function usePermissions() {
  const { user } = useAuth();
  const userRole = user?.role;

  const permissions = {
    // === 管理员专属权限 ===
    canManageUsers: userRole === 'admin',
    canViewUserList: userRole === 'admin', // 后台用户列表仅admin可访问
    canViewLogs: userRole === 'admin',
    canViewStats: userRole === 'admin',
    canBatchDelete: userRole === 'admin',

    // === 编辑者权限 (admin + editor) ===
    canManageEvents: ['admin', 'editor'].includes(userRole || ''),
    canManageCircles: ['admin', 'editor'].includes(userRole || ''),
    canManageVenues: ['admin', 'editor'].includes(userRole || ''),
    canManageImages: ['admin', 'editor'].includes(userRole || ''),
    canCreateContent: ['admin', 'editor'].includes(userRole || ''),
    canBatchApprove: ['admin', 'editor'].includes(userRole || ''),
    canAccessAdminPanel: ['admin', 'editor'].includes(userRole || ''),

    // === 登录用户权限 (admin + editor + user) ===
    canBookmark: ['admin', 'editor', 'user'].includes(userRole || ''),
    canComment: ['admin', 'editor', 'user'].includes(userRole || ''),
    canAccessProfile: ['admin', 'editor', 'user'].includes(userRole || ''),
    canEditOwnContent: ['admin', 'editor', 'user'].includes(userRole || ''),
    canDeleteOwnContent: ['admin', 'editor', 'user'].includes(userRole || ''),

    // === 公共权限 (所有角色包括未登录) ===
    canViewPublicContent: true, // 所有人都可以查看公共内容
    canViewEvents: true,
    canViewCircles: true,
    canViewVenues: true,

    // === 辅助权限检查 ===
    isLoggedIn: !!user,
    isAdmin: userRole === 'admin',
    isEditor: userRole === 'editor',
    isUser: userRole === 'user',
    isViewer: !user || userRole === 'viewer',
  };

  return permissions;
}

/**
 * 角色层级权限检查工具函数
 *
 * 权限层级：admin(4) > editor(3) > user(2) > viewer(1)
 * 高级角色自动拥有低级角色的所有权限
 */
export function hasRole(userRole: UserRole | undefined, requiredRole: UserRole): boolean {
  if (!userRole) return requiredRole === 'viewer'; // 未登录用户默认为viewer

  const roleHierarchy: Record<UserRole, number> = {
    'admin': 4,   // 最高权限
    'editor': 3,  // 编辑权限
    'user': 2,    // 基础用户权限
    'viewer': 1,  // 访客权限
  };

  return roleHierarchy[userRole] >= roleHierarchy[requiredRole];
}

/**
 * 检查是否拥有任一指定角色的权限
 */
export function hasAnyRole(userRole: UserRole | undefined, requiredRoles: UserRole[]): boolean {
  return requiredRoles.some(role => hasRole(userRole, role));
}

/**
 * 检查是否拥有所有指定角色的权限
 */
export function hasAllRoles(userRole: UserRole | undefined, requiredRoles: UserRole[]): boolean {
  return requiredRoles.every(role => hasRole(userRole, role));
}

/**
 * 获取角色的权限级别
 */
export function getRoleLevel(userRole: UserRole | undefined): number {
  if (!userRole) return 1; // 未登录默认为viewer级别

  const roleHierarchy: Record<UserRole, number> = {
    'admin': 4,
    'editor': 3,
    'user': 2,
    'viewer': 1,
  };

  return roleHierarchy[userRole] || 1;
}
