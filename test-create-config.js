// 测试创建配置的 API 调用
// 在浏览器控制台中运行此代码

async function testCreateConfig() {
  const testData = {
    entity_type: "event",
    language_code: "zh",
    label: "测试标签页",
    placeholder: "请输入测试内容...",
    icon: "test",
    sort_order: 0,
    is_active: true,
    is_preset: false
  };

  console.log('发送测试数据:', testData);

  try {
    const response = await fetch('http://localhost:8787/rich-text-tabs/configs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(testData)
    });

    console.log('响应状态:', response.status);
    console.log('响应头:', Object.fromEntries(response.headers.entries()));

    if (!response.ok) {
      const errorText = await response.text();
      console.error('错误响应:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result = await response.json();
    console.log('成功响应:', result);
    return result;
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
}

// 测试最小数据集
async function testMinimalConfig() {
  const minimalData = {
    entity_type: "event",
    language_code: "zh",
    label: "最小测试标签页"
  };

  console.log('发送最小数据:', minimalData);

  try {
    const response = await fetch('http://localhost:8787/rich-text-tabs/configs', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(minimalData)
    });

    console.log('响应状态:', response.status);

    if (!response.ok) {
      const errorText = await response.text();
      console.error('错误响应:', errorText);
      throw new Error(`HTTP ${response.status}: ${errorText}`);
    }

    const result = await response.json();
    console.log('成功响应:', result);
    return result;
  } catch (error) {
    console.error('请求失败:', error);
    throw error;
  }
}

console.log('测试函数已加载，请运行:');
console.log('testCreateConfig() - 测试完整数据');
console.log('testMinimalConfig() - 测试最小数据');
