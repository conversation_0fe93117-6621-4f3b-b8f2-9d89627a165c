"use client";

import { LucideLayoutDashboard, LucideCalendar, LucideUsers, LucideMapPin, LucideImage } from "lucide-react";
import Link from "next/link";
import { usePathname } from "next/navigation";
import React from "react";

import { useAuth } from "@/contexts/user";
import { cn } from "@/lib/utils";


interface NavItem {
  href: string;
  label: string;
  icon?: React.ReactNode;
}

const baseNav: NavItem[] = [
  { href: "/admin", label: "仪表盘", icon: <LucideLayoutDashboard className="size-4" /> },
  { href: "/admin/events", label: "展会管理", icon: <LucideCalendar className="size-4" /> },
  { href: "/admin/venues", label: "场馆管理", icon: <LucideMapPin className="size-4" /> },
  { href: "/admin/circles", label: "社团管理", icon: <LucideUsers className="size-4" /> },
  { href: "/admin/images", label: "图片管理", icon: <LucideImage className="size-4" /> },
];

// 左侧导航栏
export default function Sidebar() {
  const pathname = usePathname();
  const { user, isLoading } = useAuth();



  const navItems = React.useMemo(() => {
    if (user?.role === "admin") {
      return [
        ...baseNav,
        {
          href: "/admin/users",
          label: "用户管理",
          icon: <LucideUsers className="size-4" />,
        },
      ];
    }
    return baseNav;
  }, [user?.role, user]);

  return (
    <aside className="w-64 border-r bg-card/50 h-full p-4 flex flex-col gap-2">
      {navItems.map((item) => {
        const active = pathname === item.href;
        return (
          <Link
            key={item.href}
            href={item.href}
            className={cn(
              "flex items-center gap-3 rounded-md px-3 py-2 text-sm font-medium transition-colors",
              active
                ? "bg-primary text-primary-foreground hover:bg-primary/90"
                : "text-foreground/70 hover:bg-accent hover:text-foreground"
            )}
          >
            {item.icon}
            <span>{item.label}</span>
          </Link>
        );
      })}
    </aside>
  );
} 