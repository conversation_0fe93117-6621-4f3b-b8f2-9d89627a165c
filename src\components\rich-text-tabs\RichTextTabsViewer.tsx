'use client';

import { useState, useEffect } from 'react';
import { useLocale } from 'next-intl';
import * as Tabs from "@radix-ui/react-tabs";
import { FileText, Globe, Loader2, AlertCircle } from 'lucide-react';
import { cn } from "@/lib/utils";
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { RichTextEditor } from '../rich-text-editor/RichTextEditor';

import {
  useGetRichtexttabsTabsEntityTypeEntityIdLanguageCode,
} from '@/api/generated/ayafeedComponents';
import { type EntityType, type LanguageCode } from '@/hooks/useRichTextTabs';

export interface RichTextTabsViewerProps {
  entityType: EntityType;
  entityId: string;
  className?: string;
  showLanguageSwitch?: boolean;
  defaultLanguage?: LanguageCode;
}

// 支持的语言配置
const SUPPORTED_LANGUAGES: { code: LanguageCode; name: string; flag: string }[] = [
  { code: 'zh', name: '中文', flag: '🇨🇳' },
  { code: 'ja', name: '日本語', flag: '🇯🇵' },
  { code: 'en', name: 'English', flag: '🇺🇸' },
];

export function RichTextTabsViewer({
  entityType,
  entityId,
  className = '',
  showLanguageSwitch = true,
  defaultLanguage,
}: RichTextTabsViewerProps) {
  const locale = useLocale() as LanguageCode;
  const [selectedLanguage, setSelectedLanguage] = useState<LanguageCode>(
    defaultLanguage || locale
  );
  const [activeTabKey, setActiveTabKey] = useState<string | null>(null);
  
  // 获取标签页数据
  const {
    data: tabsData,
    isLoading,
    error,
  } = useGetRichtexttabsTabsEntityTypeEntityIdLanguageCode(
    {
      pathParams: {
        entityType,
        entityId,
        languageCode: selectedLanguage,
      },
    },
    {
      staleTime: 5 * 60 * 1000, // 5分钟缓存
    }
  );

  // 使用 useEffect 处理数据加载成功
  useEffect(() => {
    if (tabsData?.tabs && !activeTabKey && tabsData.tabs.length > 0) {
      setActiveTabKey(tabsData.tabs[0].config.key);
    }
  }, [tabsData, activeTabKey]);
  
  // 处理语言切换
  const handleLanguageChange = (language: LanguageCode) => {
    setSelectedLanguage(language);
    setActiveTabKey(null); // 重置活跃标签页
  };
  
  // 如果正在加载
  if (isLoading) {
    return (
      <div className={cn("flex items-center justify-center py-8", className)}>
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>加载内容...</span>
      </div>
    );
  }
  
  // 如果有错误
  if (error) {
    return (
      <div className={className}>
        <Alert variant="destructive">
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            加载内容失败: {error?.payload || '未知错误'}
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  // 如果没有数据或标签页
  if (!tabsData?.tabs || tabsData.tabs.length === 0) {
    return (
      <div className={className}>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            当前语言 ({selectedLanguage}) 暂无可用内容
          </AlertDescription>
        </Alert>
      </div>
    );
  }

  const activeTabs = tabsData.tabs.filter(tab => tab.config.is_active);
  
  // 如果没有活跃的标签页
  if (activeTabs.length === 0) {
    return (
      <div className={className}>
        <Alert>
          <AlertCircle className="h-4 w-4" />
          <AlertDescription>
            当前语言暂无活跃的内容标签页
          </AlertDescription>
        </Alert>
      </div>
    );
  }
  
  return (
    <div className={className}>
      {/* 头部：标题和语言切换 */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <FileText className="h-5 w-5 text-muted-foreground" />
          <h3 className="text-lg font-semibold">详细信息</h3>
          <Badge variant="outline" className="text-xs">
            新版本
          </Badge>
        </div>
        
        {/* 语言切换器 */}
        {showLanguageSwitch && (
          <div className="flex items-center gap-2">
            <Globe className="h-4 w-4 text-muted-foreground" />
            <div className="flex gap-1">
              {SUPPORTED_LANGUAGES.map((lang) => (
                <Button
                  key={lang.code}
                  variant={selectedLanguage === lang.code ? "default" : "outline"}
                  size="sm"
                  onClick={() => handleLanguageChange(lang.code)}
                  className="h-8 px-2"
                >
                  <span className="mr-1">{lang.flag}</span>
                  {lang.name}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>
      
      <Tabs.Root
        value={activeTabKey || ''}
        onValueChange={setActiveTabKey}
        className="w-full"
      >
        {/* 标签页导航 */}
        <Tabs.List className="flex w-full gap-1 mb-6 p-1 bg-muted rounded-lg overflow-x-auto">
          {activeTabs.map((tab) => (
            <Tabs.Trigger
              key={tab.config.key}
              value={tab.config.key}
              className={cn(
                "flex items-center gap-2 px-4 py-2 text-sm font-medium rounded-md whitespace-nowrap",
                "data-[state=active]:bg-background data-[state=active]:shadow-sm",
                "hover:bg-background/50 transition-colors"
              )}
            >
              <span>{tab.config.label}</span>
              {tab.config.is_preset && (
                <Badge variant="secondary" className="text-xs">
                  预设
                </Badge>
              )}
            </Tabs.Trigger>
          ))}
        </Tabs.List>
        
        {/* 标签页内容 */}
        {activeTabs.map((tab) => (
          <Tabs.Content
            key={tab.config.key}
            value={tab.config.key}
            className="mt-0 ring-offset-background focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2"
          >
            {/* 标签页占位符作为描述 */}
            {tab.config.placeholder && (
              <div className="text-sm text-muted-foreground bg-muted/50 p-3 rounded-lg mb-4">
                {tab.config.placeholder}
              </div>
            )}
            
            {/* 内容显示 */}
            <div className="prose prose-sm max-w-none">
              {tab.content?.content ? (
                <RichTextEditor
                  content={tab.content.content}
                  editable={false}
                  className="border-0 p-0"
                />
              ) : (
                <div className="text-muted-foreground italic py-8 text-center">
                  暂无 {tab.config.label} 内容
                </div>
              )}
            </div>
          </Tabs.Content>
        ))}
      </Tabs.Root>
    </div>
  );
}
