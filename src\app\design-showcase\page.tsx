"use client";

import { motion } from 'motion/react';
import { Calendar, MapPin, Users, Star, AlertTriangle, Clock, Tag, Search, Mail, Eye, EyeOff } from 'lucide-react';
import { 
  ModernPageLayout, 
  Container, 
  GradientHeading, 
  Subtitle, 
  ModernCard, 
  ModernButton,
  ModernInput,
  ResponsiveGrid
} from '@/components/design-system';
import { NewsCard, NewsCardGrid, CompactNewsCard } from '@/components/ui/news-card';
import { Breadcrumb } from '@/components/ui/breadcrumb';
import { Button } from '@/components/ui/button';

export default function DesignShowcase() {
  return (
    <ModernPageLayout>
      <Container>
        {/* 面包屑导航 */}
        <Breadcrumb 
          items={[
            { label: '设计系统', href: '/design-showcase' }
          ]}
        />

        {/* 页面标题 */}
        <div className="text-center space-y-6 py-16">
          <motion.div
            initial={{ opacity: 0, y: 30 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <GradientHeading level={1} gradient="accent">
              射命丸文新闻主题设计系统
            </GradientHeading>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.2, duration: 0.6 }}
          >
            <Subtitle size="lg" className="max-w-3xl mx-auto">
              基于现代设计原则和射命丸文记者主题的专业设计系统展示。
              融合了新闻媒体特色、风的动画效果和优雅的用户体验。
            </Subtitle>
          </motion.div>
        </div>

        {/* 色彩系统展示 */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <GradientHeading level={2} gradient="brand" className="mb-8">
            色彩系统
          </GradientHeading>
          
          <ResponsiveGrid cols={{ default: 1, md: 2, lg: 4 }} gap={6}>
            <ModernCard className="text-center">
              <div className="w-16 h-16 bg-primary rounded-xl mx-auto mb-4"></div>
              <h3 className="font-semibold text-primary">主色 Primary</h3>
              <p className="text-sm text-muted-foreground mt-2">新闻红色调</p>
            </ModernCard>
            
            <ModernCard className="text-center">
              <div className="w-16 h-16 bg-secondary rounded-xl mx-auto mb-4"></div>
              <h3 className="font-semibold">辅助色 Secondary</h3>
              <p className="text-sm text-muted-foreground mt-2">天狗黑色调</p>
            </ModernCard>
            
            <ModernCard className="text-center">
              <div className="w-16 h-16 bg-accent rounded-xl mx-auto mb-4"></div>
              <h3 className="font-semibold text-accent-foreground">强调色 Accent</h3>
              <p className="text-sm text-muted-foreground mt-2">金色强调</p>
            </ModernCard>
            
            <ModernCard className="text-center">
              <div className="w-16 h-16 bg-muted rounded-xl mx-auto mb-4"></div>
              <h3 className="font-semibold">中性色 Muted</h3>
              <p className="text-sm text-muted-foreground mt-2">背景色调</p>
            </ModernCard>
          </ResponsiveGrid>
        </motion.section>

        {/* 按钮组件展示 */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <GradientHeading level={2} gradient="brand" className="mb-8">
            按钮组件
          </GradientHeading>
          
          <ModernCard>
            <div className="space-y-6">
              <div>
                <h3 className="font-semibold mb-4">按钮变体</h3>
                <div className="flex flex-wrap gap-4">
                  <Button variant="default">主要按钮</Button>
                  <Button variant="secondary">次要按钮</Button>
                  <Button variant="outline">轮廓按钮</Button>
                  <Button variant="ghost">幽灵按钮</Button>
                  <Button variant="accent">强调按钮</Button>
                  <Button variant="destructive">危险按钮</Button>
                </div>
              </div>
              
              <div>
                <h3 className="font-semibold mb-4">按钮尺寸</h3>
                <div className="flex flex-wrap items-center gap-4">
                  <Button size="sm">小按钮</Button>
                  <Button size="default">默认按钮</Button>
                  <Button size="lg">大按钮</Button>
                  <Button size="icon">
                    <Star className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          </ModernCard>
        </motion.section>

        {/* 新闻卡片展示 */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <GradientHeading level={2} gradient="brand" className="mb-8">
            新闻卡片组件
          </GradientHeading>
          
          <NewsCardGrid>
            <NewsCard
              title="重要展会公告：C103即将开幕"
              excerpt="第103届Comic Market即将于2024年12月在东京国际展示场举办，预计将有超过35,000个社团参展..."
              publishTime="2小时前"
              category="展会公告"
              urgent={true}
              imageUrl="https://images.unsplash.com/photo-1540575467063-178a50c2df87?w=400&h=200&fit=crop"
            />
            
            <NewsCard
              title="新社团入驻平台"
              excerpt="本周有超过50个新社团加入我们的平台，涵盖了各种不同的创作领域和风格..."
              publishTime="1天前"
              category="平台动态"
              featured={true}
              imageUrl="https://images.unsplash.com/photo-1513475382585-d06e58bcb0e0?w=400&h=200&fit=crop"
            />
            
            <NewsCard
              title="创作者访谈系列"
              excerpt="本期我们采访了知名同人作家，分享他们的创作心得和参展经验..."
              publishTime="3天前"
              category="创作者访谈"
              imageUrl="https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=400&h=200&fit=crop"
            />
          </NewsCardGrid>
          
          <div className="mt-8">
            <h3 className="font-semibold mb-4">紧凑型新闻卡片</h3>
            <div className="space-y-3">
              <CompactNewsCard
                title="展会门票开始预售"
                publishTime="30分钟前"
                category="票务信息"
                urgent={true}
              />
              <CompactNewsCard
                title="新功能上线：社团地图导航"
                publishTime="2小时前"
                category="功能更新"
              />
              <CompactNewsCard
                title="本周热门社团推荐"
                publishTime="1天前"
                category="推荐"
              />
            </div>
          </div>
        </motion.section>

        {/* 表单组件展示 */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <GradientHeading level={2} gradient="brand" className="mb-8">
            表单组件
          </GradientHeading>
          
          <div className="space-y-8">
            <ModernCard>
              <h3 className="text-lg font-semibold mb-6">基础表单组件</h3>
              <div className="space-y-6">
                <ModernInput
                  label="搜索展会"
                  placeholder="输入展会名称或关键词..."
                  leftIcon={<Search className="h-4 w-4" />}
                  helperText="支持模糊搜索和关键词匹配"
                />

                <ModernInput
                  label="邮箱地址"
                  type="email"
                  placeholder="<EMAIL>"
                  leftIcon={<Mail className="h-4 w-4" />}
                  error="请输入有效的邮箱地址"
                />

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <ModernInput
                    label="开始日期"
                    type="date"
                    leftIcon={<Calendar className="h-4 w-4" />}
                  />
                  <ModernInput
                    label="结束日期"
                    type="date"
                    leftIcon={<Calendar className="h-4 w-4" />}
                  />
                </div>
              </div>
            </ModernCard>

            <ModernCard>
              <h3 className="text-lg font-semibold mb-6">按钮状态展示</h3>
              <div className="space-y-6">
                <div>
                  <h4 className="font-medium mb-4">基础按钮</h4>
                  <div className="flex flex-wrap gap-4">
                    <ModernButton variant="primary" icon={<Search className="h-4 w-4" />}>
                      搜索
                    </ModernButton>
                    <ModernButton variant="secondary">重置</ModernButton>
                    <ModernButton variant="outline" disabled>
                      已禁用
                    </ModernButton>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-4">加载状态</h4>
                  <div className="flex flex-wrap gap-4">
                    <ModernButton variant="primary" loading loadingText="搜索中...">
                      搜索
                    </ModernButton>
                    <ModernButton variant="accent" loading>
                      提交
                    </ModernButton>
                  </div>
                </div>

                <div>
                  <h4 className="font-medium mb-4">图标按钮</h4>
                  <div className="flex flex-wrap gap-4">
                    <ModernButton
                      variant="outline"
                      icon={<Eye className="h-4 w-4" />}
                      iconPosition="left"
                    >
                      查看详情
                    </ModernButton>
                    <ModernButton
                      variant="ghost"
                      icon={<Star className="h-4 w-4" />}
                      iconPosition="right"
                    >
                      收藏
                    </ModernButton>
                  </div>
                </div>
              </div>
            </ModernCard>
          </div>
        </motion.section>

        {/* 可访问性功能展示 */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <GradientHeading level={2} gradient="brand" className="mb-8">
            可访问性功能
          </GradientHeading>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            <ModernCard>
              <h3 className="text-lg font-semibold mb-4">键盘导航</h3>
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  所有交互元素都支持键盘导航：
                </p>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• Tab 键切换焦点</li>
                  <li>• Enter/Space 激活按钮</li>
                  <li>• 箭头键导航菜单</li>
                  <li>• Esc 键关闭弹窗</li>
                </ul>
              </div>
            </ModernCard>

            <ModernCard>
              <h3 className="text-lg font-semibold mb-4">屏幕阅读器支持</h3>
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  完整的 ARIA 标签和语义化标记：
                </p>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• aria-label 描述</li>
                  <li>• role 属性定义</li>
                  <li>• aria-describedby 关联</li>
                  <li>• 状态变化通知</li>
                </ul>
              </div>
            </ModernCard>

            <ModernCard>
              <h3 className="text-lg font-semibold mb-4">色彩对比度</h3>
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  符合 WCAG 2.1 AA 标准：
                </p>
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-primary rounded"></div>
                    <span className="text-sm">主色对比度 4.5:1</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="w-4 h-4 bg-secondary rounded"></div>
                    <span className="text-sm">辅助色对比度 7:1</span>
                  </div>
                </div>
              </div>
            </ModernCard>

            <ModernCard>
              <h3 className="text-lg font-semibold mb-4">响应式设计</h3>
              <div className="space-y-3">
                <p className="text-sm text-muted-foreground">
                  适配所有设备和屏幕尺寸：
                </p>
                <ul className="text-sm space-y-1 text-muted-foreground">
                  <li>• 移动端优先设计</li>
                  <li>• 触摸友好的交互区域</li>
                  <li>• 自适应字体大小</li>
                  <li>• 灵活的布局系统</li>
                </ul>
              </div>
            </ModernCard>
          </div>
        </motion.section>

        {/* 装饰元素展示 */}
        <motion.section
          className="mb-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <GradientHeading level={2} gradient="brand" className="mb-8">
            主题装饰元素
          </GradientHeading>

          <ModernCard className="relative overflow-hidden min-h-[200px]">
            <div className="text-center py-8">
              <h3 className="text-xl font-semibold mb-4">风的动画效果</h3>
              <p className="text-muted-foreground">
                悬停在卡片上可以看到射命丸文主题的风动画效果
              </p>
            </div>

            {/* 演示用的风装饰 */}
            <div className="wind-decoration top-8 right-16"></div>
            <div className="wind-decoration-reverse top-16 left-20"></div>
            <div className="wind-decoration top-24 right-32"></div>
          </ModernCard>
        </motion.section>

        {/* 总结 */}
        <motion.section
          className="text-center py-16"
          initial={{ opacity: 0, y: 50 }}
          whileInView={{ opacity: 1, y: 0 }}
          viewport={{ once: true }}
          transition={{ duration: 0.6 }}
        >
          <GradientHeading level={2} gradient="accent" className="mb-6">
            设计系统特色
          </GradientHeading>
          
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-12">
            <div className="text-center">
              <div className="w-16 h-16 bg-primary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Tag className="h-8 w-8 text-primary" />
              </div>
              <h3 className="font-semibold mb-2">专业新闻主题</h3>
              <p className="text-sm text-muted-foreground">
                基于射命丸文记者身份设计的专业新闻媒体风格
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-accent/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Star className="h-8 w-8 text-accent" />
              </div>
              <h3 className="font-semibold mb-2">现代交互体验</h3>
              <p className="text-sm text-muted-foreground">
                流畅的动画效果和直观的用户界面设计
              </p>
            </div>
            
            <div className="text-center">
              <div className="w-16 h-16 bg-secondary/10 rounded-xl flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-secondary-foreground" />
              </div>
              <h3 className="font-semibold mb-2">可访问性优先</h3>
              <p className="text-sm text-muted-foreground">
                遵循WCAG标准，确保所有用户都能良好使用
              </p>
            </div>
          </div>
        </motion.section>
      </Container>
    </ModernPageLayout>
  );
}
