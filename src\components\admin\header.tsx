"use client";

import { cn } from "@/lib/utils";
import { useLogoutMutation } from "@/hooks/useAuthMutations";

// 纯 Radix <PERSON>ton 组件 (复用之前的实现)
interface ButtonProps extends React.ButtonHTMLAttributes<HTMLButtonElement> {
  variant?: 'default' | 'ghost' | 'outline';
  size?: 'default' | 'sm' | 'lg' | 'icon';
  asChild?: boolean;
}

function RadixButton({
  className,
  variant = 'default',
  size = 'default',
  children,
  ...props
}: ButtonProps) {
  const baseStyles = "inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 outline-none focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2";

  const variants = {
    default: "bg-primary text-primary-foreground shadow-sm hover:bg-primary/90",
    ghost: "hover:bg-accent hover:text-accent-foreground",
    outline: "border border-input bg-background hover:bg-accent hover:text-accent-foreground",
  };

  const sizes = {
    default: "h-10 px-4 py-2",
    sm: "h-9 rounded-md px-3",
    lg: "h-11 rounded-md px-8",
    icon: "h-10 w-10",
  };

  return (
    <button
      type="button"
      className={cn(
        baseStyles,
        variants[variant],
        sizes[size],
        className
      )}
      {...props}
    >
      {children}
    </button>
  );
}

export default function AdminHeader() {
  const logoutMutation = useLogoutMutation();

  const handleLogout = () => {
    logoutMutation.mutate(); // Better Auth 不需要参数，使用 cookies
  };

  return (
    <header className="h-14 border-b flex items-center justify-between px-6 bg-card/60 backdrop-blur-sm">
      <div className="font-semibold">后台管理系统</div>
      <RadixButton
        variant="ghost"
        size="sm"
        onClick={handleLogout}
        disabled={logoutMutation.isPending}
      >
        {logoutMutation.isPending ? '登出中...' : '退出登录'}
      </RadixButton>
    </header>
  );
}