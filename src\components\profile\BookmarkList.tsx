'use client';

import React from 'react';
import { motion, AnimatePresence } from 'motion/react';
import { Bookmark } from 'lucide-react';
import { BookmarkCard } from './BookmarkCard';

// 收藏项类型
interface BookmarkItem {
  id: string;
  type: 'circle';
  title: string;
  description: string;
  urls: string | null;
  bookmarkedAt: string;
  circleId: string;
}

interface BookmarkListProps {
  items: BookmarkItem[];
  viewMode: 'grid' | 'list';
  onRemoveBookmark: (circleId: string) => void;
  isRemoving?: boolean;
  searchQuery: string;
}

export function BookmarkList({ 
  items, 
  viewMode, 
  onRemoveBookmark, 
  isRemoving = false,
  searchQuery 
}: BookmarkListProps) {
  if (items.length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
        className="flex flex-col items-center justify-center py-16 text-center"
      >
        <Bookmark className="h-16 w-16 text-stone-300 dark:text-stone-600 mx-auto mb-4" />
        <h3 className="text-lg font-medium text-stone-900 dark:text-stone-100 mb-2">
          暂无收藏
        </h3>
        <p className="text-stone-600 dark:text-stone-400">
          {searchQuery
            ? '没有找到匹配的收藏项目'
            : '开始收藏您感兴趣的社团吧'}
        </p>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      <div className={
        viewMode === 'grid' 
          ? 'grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6'
          : 'space-y-4'
      }>
        <AnimatePresence mode="popLayout">
          {items.map((item) => (
            <BookmarkCard
              key={item.id}
              item={item}
              viewMode={viewMode}
              onRemove={onRemoveBookmark}
              isRemoving={isRemoving}
            />
          ))}
        </AnimatePresence>
      </div>
    </motion.div>
  );
}
