# 错误处理测试

## 修改内容

已更新 `useRichTextTabs.ts` 中的错误处理逻辑，现在会显示具体的错误信息而不是通用的错误提示。

### 主要改进

1. **新增 `getDetailedErrorMessage` 函数**
   - 解析多种错误格式
   - 优先显示字段错误信息
   - 支持业务错误码和消息

2. **更新所有错误处理**
   - 创建标签页错误
   - 更新标签页错误
   - 删除标签页错误
   - 排序错误

### 错误格式支持

函数现在支持以下错误格式：

```javascript
// 1. 字段错误 (最优先)
{
  payload: {
    detail: {
      fieldErrors: {
        errors: "排序值 5 已被使用"
      }
    }
  }
}

// 2. 业务错误
{
  payload: {
    code: 10001,
    message: "参数无效"
  }
}

// 3. 直接响应错误
{
  data: {
    code: 10001,
    message: "参数无效",
    detail: {
      fieldErrors: {
        errors: "排序值 5 已被使用"
      }
    }
  }
}

// 4. 简单错误
{
  message: "网络错误"
}
```

### 测试场景

现在当用户遇到以下情况时，会看到具体的错误信息：

1. **排序值冲突**: "排序值 5 已被使用"
2. **标签名称重复**: "标签名称已存在"
3. **参数验证失败**: "标签名称不能为空"
4. **权限错误**: "权限不足 (错误码: 20002)"

### 用户体验改进

- ✅ 显示具体错误原因
- ✅ 保留错误码信息
- ✅ 友好的中文提示
- ✅ 调试信息记录

## 测试方法

1. 尝试创建排序值重复的标签页
2. 尝试创建空标签名称的标签页
3. 检查错误提示是否显示具体原因

## 预期结果

用户现在应该看到类似以下的错误提示：
- "排序值 5 已被使用" (而不是 "创建标签页失败，请重试")
- "标签名称不能为空" (而不是 "参数无效")
- "权限不足 (错误码: 20002)" (包含错误码信息)
