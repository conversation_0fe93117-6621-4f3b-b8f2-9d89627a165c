# Placeholder 显示修复说明

## 🐛 问题分析

Placeholder 没有正确显示的问题有以下几个原因：

### 1. **空内容检测不准确**
```json
// 这种内容被认为"有内容"，但实际上是空的
{
  "type": "doc",
  "content": [
    {
      "type": "paragraph",
      "attrs": {
        "textAlign": null
      }
    }
  ]
}
```

### 2. **CSS 选择器不匹配**
原始的 CSS 依赖于 Tiptap 自动添加的 `is-editor-empty` 类，但这个类在某些情况下不会被添加。

### 3. **缺少 Placeholder 扩展**
项目中没有安装 `@tiptap/extension-placeholder` 扩展。

## 🔧 修复方案

### 1. **改进空内容检测逻辑**

```typescript
// 新增的 isEmptyContent 函数
const isEmptyContent = (content: any): boolean => {
  if (!content) return true;
  
  // 检查字符串内容
  if (typeof content === 'string') {
    const trimmed = content.trim();
    if (!trimmed) return true;
    
    // 检查是否是空的 JSON 结构
    if (trimmed.startsWith('{')) {
      try {
        const parsed = JSON.parse(trimmed);
        return isEmptyContent(parsed);
      } catch {
        return false;
      }
    }
    return false;
  }
  
  // 检查 Tiptap 文档结构
  if (typeof content === 'object' && content.type === 'doc') {
    if (!content.content || content.content.length === 0) return true;
    
    // 检查是否只包含空段落
    return content.content.every((node: any) => {
      if (node.type === 'paragraph') {
        return !node.content || node.content.length === 0;
      }
      return false;
    });
  }
  
  return false;
};
```

### 2. **改进 parseContent 函数**

```typescript
const parseContent = (rawContent: string) => {
  // ... 解析逻辑
  
  // 如果解析后的内容为空，返回空文档
  if (isEmptyContent(parsed)) {
    return {
      type: 'doc',
      content: []
    };
  }
  
  return parsed;
};
```

### 3. **添加自定义 Placeholder 显示**

```typescript
// 使用状态跟踪内容是否为空
const [isEmpty, setIsEmpty] = useState(true);

// 在编辑器更新时更新状态
onUpdate: ({ editor }) => {
  const json = editor.getJSON();
  onChange?.(JSON.stringify(json));
  setIsEmpty(isEmptyContent(json));
},

// 在渲染中显示自定义 placeholder
{isEmpty && placeholder && (
  <div className="absolute top-4 left-4 text-gray-400 pointer-events-none select-none">
    {placeholder}
  </div>
)}
```

### 4. **改进 CSS 选择器**

```css
/* 多种情况下的 placeholder 显示 */
.ProseMirror.ProseMirror-focused p.is-editor-empty:first-child::before,
.ProseMirror p.is-editor-empty:first-child::before {
  content: attr(data-placeholder);
  @apply text-gray-400 pointer-events-none float-left h-0;
}

/* 当编辑器完全为空时 */
.ProseMirror:empty::before {
  content: attr(data-placeholder);
  @apply text-gray-400 pointer-events-none absolute;
}

/* 当编辑器只有空段落时 */
.ProseMirror > p:only-child:empty::before {
  content: attr(data-placeholder);
  @apply text-gray-400 pointer-events-none;
}
```

## 🎯 修复效果

### 现在支持的空内容格式

1. **完全空的内容**：
   ```typescript
   content = ""
   content = null
   content = undefined
   ```

2. **空的 JSON 文档**：
   ```json
   {
     "type": "doc",
     "content": []
   }
   ```

3. **只包含空段落的文档**：
   ```json
   {
     "type": "doc",
     "content": [
       {
         "type": "paragraph",
         "attrs": { "textAlign": null }
       }
     ]
   }
   ```

4. **JSON 字符串格式的空内容**：
   ```typescript
   content = '{"type":"doc","content":[{"type":"paragraph","attrs":{"textAlign":null}}]}'
   ```

### Placeholder 显示逻辑

1. **双重保障**：
   - CSS 选择器处理标准情况
   - JavaScript 自定义显示处理特殊情况

2. **实时更新**：
   - 编辑器内容变化时立即更新 placeholder 显示状态
   - 外部 content 属性变化时同步更新

3. **样式一致**：
   - 使用相同的灰色文本样式
   - 正确的定位和层级

## 🧪 测试场景

### 1. **空内容测试**
```typescript
// 这些情况都应该显示 placeholder
<RichTextEditor content="" placeholder="请输入内容..." />
<RichTextEditor content='{"type":"doc","content":[]}' placeholder="请输入内容..." />
<RichTextEditor content='{"type":"doc","content":[{"type":"paragraph","attrs":{"textAlign":null}}]}' placeholder="请输入内容..." />
```

### 2. **有内容测试**
```typescript
// 这些情况不应该显示 placeholder
<RichTextEditor content="Hello World" placeholder="请输入内容..." />
<RichTextEditor content='{"type":"doc","content":[{"type":"paragraph","content":[{"type":"text","text":"Hello"}]}]}' placeholder="请输入内容..." />
```

### 3. **动态内容测试**
```typescript
// 内容从空变为有内容，placeholder 应该消失
const [content, setContent] = useState('');
<RichTextEditor 
  content={content} 
  onChange={setContent}
  placeholder="开始输入..." 
/>
```

## 🎉 最终效果

现在 RichTextEditor 在以下情况下都会正确显示 placeholder：

- ✅ **完全空的编辑器**
- ✅ **只有空段落的编辑器**
- ✅ **从后端获取的空 JSON 内容**
- ✅ **用户清空所有内容后**
- ✅ **实时内容变化时的状态更新**

Placeholder 会在用户开始输入时立即消失，在内容被清空时立即显示，提供了完美的用户体验！

## 🔧 在 RichTextTabsManager 中的应用

在富文本标签页管理器中，每个标签页都会显示对应的 placeholder：

```typescript
<RichTextEditor
  content={getTabContent(config.key)}
  onChange={handleContentChange}
  placeholder={config.placeholder || `请输入 ${config.label} 内容...`}
  className="min-h-[300px]"
/>
```

现在用户可以清楚地看到每个标签页期望的内容类型，大大提升了编辑体验！
