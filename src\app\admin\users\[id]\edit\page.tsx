"use client";

import { usePara<PERSON>, useRouter } from "next/navigation";
import { useEffect, useState } from "react";

import { Button } from "@/components/ui/button";
import { useAdminUserDetail } from "@/hooks/admin/useAdminUserDetail";
import { useUpdateUser } from "@/hooks/admin/useUpdateUser";

interface UserForm {
  id: string;
  username: string;
  role: "admin" | "editor" | "user" | "viewer";
}

export default function EditUserPage() {
  const router = useRouter();
  const { id } = useParams<{ id: string }>();
  const { data: detail, isLoading: detailLoading } = useAdminUserDetail(id);
  const updateUser = useUpdateUser(id);
  const [form, setForm] = useState<UserForm | null>(null);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    if (!detailLoading && detail) {
      setForm({ id: detail.id, username: detail.username || '', role: detail.role });
    }
  }, [detailLoading, detail]);

  function handleChange(e: React.ChangeEvent<HTMLInputElement | HTMLSelectElement>) {
    if (!form) return;
    setForm({ ...form, [e.target.name]: e.target.value });
  }

  function handleSubmit(e: React.FormEvent) {
    e.preventDefault();
    if (!form) return;
    const { id: _skip, ...payload } = form;
    updateUser.mutate(payload, {
      onSuccess: () => { router.push("/admin/users"); },
      onError: (err: any) => { setError(err?.message ?? "更新失败"); },
    });
  }

  if (detailLoading || !form) return <p>加载中...</p>;

  return (
    <div className="max-w-xl space-y-4">
      <h1 className="text-2xl font-bold">编辑用户</h1>
      {error && <p className="text-destructive text-sm">{error}</p>}
      <form onSubmit={handleSubmit} className="space-y-4">
        <div>
          <label className="block text-sm mb-1">用户名</label>
          <input
            type="text"
            value={form.username}
            name="username"
            disabled
            className="w-full border rounded px-3 py-2 bg-muted/50"
          />
        </div>
        <div>
          <label className="block text-sm mb-1">角色</label>
          <select
            name="role"
            value={form.role}
            onChange={handleChange}
            className="w-full border rounded px-3 py-2"
          >
            <option value="viewer">Viewer</option>
            <option value="editor">Editor</option>
            <option value="admin">Admin</option>
          </select>
        </div>
        <Button type="submit" disabled={updateUser.isPending}>
          {updateUser.isPending ? "保存中..." : "保存"}
        </Button>
      </form>
    </div>
  );
} 