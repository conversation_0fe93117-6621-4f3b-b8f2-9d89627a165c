import { NextResponse, type NextRequest } from "next/server";

// 中间件：检查 /admin 路由访问权限
export function middleware(request: NextRequest) {
  const { pathname } = request.nextUrl;

  // 临时禁用中间件检查，让 RoleGuard 处理权限控制
  // TODO: 修复 cookie 同步问题后重新启用
  /*
  // 仅拦截 /admin 及其子路由
  if (pathname.startsWith("/admin")) {
    // 使用 Better Auth 的 cookie 名称
    const authToken = request.cookies.get("better-auth.session_token");
    const hasToken = Boolean(authToken?.value);

    if (!hasToken) {
      // 未登录则重定向到 /login，并附带回跳 URL
      const loginUrl = new URL("/login", request.url);
      loginUrl.searchParams.set("redirect", pathname);
      return NextResponse.redirect(loginUrl);
    }
  }
  */
  return NextResponse.next();
}

export const config = {
  matcher: ["/admin", "/admin/:path*"],
};